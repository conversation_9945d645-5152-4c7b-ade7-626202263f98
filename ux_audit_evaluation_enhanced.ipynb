{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced UX Audit Report Evaluation Using Gemini AI\n", "\n", "This notebook evaluates model-generated UX audit reports against ground truth data using Google's Gemini AI model with enhanced descriptions.\n", "\n", "## Key Features:\n", "- **Clear Location Analysis** - Detailed comparison of what's same, missing, or extra\n", "- **Detailed Observation Analysis** - Depth comparison with specific missing/extra points\n", "- **Enhanced Heuristic Matching** - Clear count and detailed breakdown\n", "- **Weighted Scoring** - Location 30%, Observation 30%, Heuristic 25%, Severity 15%"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-generativeai pandas numpy mat<PERSON><PERSON><PERSON>b seaborn scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Any\n", "import google.generativeai as genai\n", "from sklearn.metrics import precision_recall_fscore_support\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure Gemini API\n", "# Replace 'YOUR_API_KEY' with your actual Gemini API key\n", "GEMINI_API_KEY = 'YOUR_API_KEY'  # Get from https://makersuite.google.com/app/apikey\n", "genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "# Initialize Gemini model\n", "model = genai.GenerativeModel('gemini-1.5-flash')\n", "\n", "print(\"✅ Gemini AI configured successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data files\n", "def load_json_data(file_path: str) -> Dict:\n", "    \"\"\"Load JSON data from file\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return json.load(f)\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return {}\n", "\n", "# Load ground truth and model response data\n", "ground_truth = load_json_data('ground_truth.json')\n", "model_response = load_json_data('model_response.json')\n", "\n", "# Extract observations\n", "gt_observations = ground_truth.get('audit_report', {}).get('observations', [])\n", "model_observations = model_response.get('audit_report', {}).get('observations', [])\n", "\n", "print(f\"📊 Loaded {len(gt_observations)} ground truth observations\")\n", "print(f\"📊 Loaded {len(model_observations)} model response observations\")\n", "print(\"\\n✅ Data loaded successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_gemini_similarity_score(text1: str, text2: str, context: str = \"\") -> Dict:\n", "    \"\"\"Get semantic similarity score and analysis from Gemini\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two UX audit observations and provide:\n", "    1. Similarity score (0-100)\n", "    2. Brief analysis of similarities and differences\n", "    3. Whether they refer to the same UX issue (yes/no)\n", "    \n", "    Context: {context}\n", "    \n", "    Text 1: {text1}\n", "    Text 2: {text2}\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"similarity_score\": <0-100>,\n", "        \"same_issue\": <true/false>,\n", "        \"analysis\": \"<brief analysis>\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error in Gemini API call: {e}\")\n", "        return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": f\"API Error: {e}\"}\n", "\n", "print(\"🔧 Gemini similarity function ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Optimized Observation Matching\n", "\n", "Find optimal 1:1 matches between ground truth and model observations using efficient Gemini analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_observation_matches_optimized(gt_obs: List[Dict], model_obs: List[Dict]) -> List[Dict]:\n", "    \"\"\"Find optimal 1:1 matches between GT and model observations using Gemini efficiently\"\"\"\n", "    print(\"🔍 Finding optimal observation matches using Gemini AI...\")\n", "    print(f\"📊 Processing {len(gt_obs)} GT observations vs {len(model_obs)} model observations\")\n", "    \n", "    # Step 1: Batch similarity analysis for all pairs\n", "    print(\"\\n🚀 Step 1: <PERSON><PERSON> analyzing all GT-Model pairs...\")\n", "    \n", "    # Create batch prompt for all comparisons to reduce API calls\n", "    batch_prompt = f\"\"\"\n", "    Compare these UX audit observations and provide similarity scores for each pair.\n", "    For each comparison, consider both location similarity and observation content similarity.\n", "    \n", "    GROUND TRUTH OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": gt[\"location\"], \"observation\": gt[\"observation\"][:200] + \"...\"} for i, gt in enumerate(gt_obs)], indent=2)}\n", "    \n", "    MODEL RESPONSE OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": model[\"location\"], \"observation\": model[\"observation\"][:200] + \"...\"} for i, model in enumerate(model_obs)], indent=2)}\n", "    \n", "    For each GT observation, find the best matching model observation and provide:\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"matches\": [\n", "            {{\n", "                \"gt_id\": <gt_index>,\n", "                \"best_model_id\": <model_index or -1 if no good match>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "        ]\n", "    }}\n", "    \n", "    Only consider matches with combined_score > 40. Set best_model_id to -1 if no good match exists.\n", "    \"\"\"\n", "    \n", "    try:\n", "        print(\"🤖 Sending batch request to Gemini...\")\n", "        response = model.generate_content(batch_prompt)\n", "        \n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            batch_results = json.loads(json_match.group())\n", "            potential_matches = batch_results.get('matches', [])\n", "            print(f\"✅ Batch analysis complete! Found {len(potential_matches)} potential matches\")\n", "        else:\n", "            print(\"❌ Failed to parse batch response, falling back to individual analysis\")\n", "            potential_matches = []\n", "    except Exception as e:\n", "        print(f\"❌ Batch analysis failed: {e}\")\n", "        print(\"🔄 Falling back to optimized individual analysis...\")\n", "        potential_matches = []\n", "    \n", "    # Step 2: If batch failed, use optimized individual analysis\n", "    if not potential_matches:\n", "        potential_matches = []\n", "        for i, gt_item in enumerate(gt_obs):\n", "            print(f\"\\n📍 Processing GT {i+1}/{len(gt_obs)}: {gt_item['location'][:30]}...\")\n", "            \n", "            # Quick location-based filtering first\n", "            candidate_models = []\n", "            for j, model_item in enumerate(model_obs):\n", "                # Simple keyword overlap check for initial filtering\n", "                gt_keywords = set(gt_item['location'].lower().split())\n", "                model_keywords = set(model_item['location'].lower().split())\n", "                keyword_overlap = len(gt_keywords.intersection(model_keywords)) / max(len(gt_keywords), 1)\n", "                \n", "                if keyword_overlap > 0.2:  # At least 20% keyword overlap\n", "                    candidate_models.append((j, model_item, keyword_overlap))\n", "            \n", "            # Sort by keyword overlap and take top 3 candidates\n", "            candidate_models.sort(key=lambda x: x[2], reverse=True)\n", "            top_candidates = candidate_models[:3]\n", "            \n", "            if not top_candidates:\n", "                print(f\"  ❌ No location candidates found\")\n", "                potential_matches.append({\n", "                    'gt_id': i,\n", "                    'best_model_id': -1,\n", "                    'combined_score': 0,\n", "                    'is_valid_match': <PERSON><PERSON><PERSON>,\n", "                    'reasoning': 'No location similarity found'\n", "                })\n", "                continue\n", "            \n", "            # Use Gemini only for top candidates\n", "            candidates_text = \"\\n\".join([f\"Model {idx}: {item['location']} - {item['observation'][:100]}...\" \n", "                                        for idx, item, _ in top_candidates])\n", "            \n", "            candidate_prompt = f\"\"\"\n", "            Find the best match for this GT observation among the candidates:\n", "            \n", "            GT Observation:\n", "            Location: {gt_item['location']}\n", "            Description: {gt_item['observation'][:200]}...\n", "            \n", "            Candidates:\n", "            {candidates_text}\n", "            \n", "            Respond in JSON format:\n", "            {{\n", "                \"best_model_id\": <model_index or -1>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(candidate_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    result = json.loads(json_match.group())\n", "                    result['gt_id'] = i\n", "                    potential_matches.append(result)\n", "                    if result['is_valid_match']:\n", "                        print(f\"  ✅ Match found: Model {result['best_model_id']} (Score: {result['combined_score']})\")\n", "                    else:\n", "                        print(f\"  ❌ No valid match (Best score: {result['combined_score']})\")\n", "                else:\n", "                    print(f\"  ❌ Failed to parse response\")\n", "            except Exception as e:\n", "                print(f\"  ❌ Error: {e}\")\n", "    \n", "    # Step 3: Resolve conflicts and create optimal 1:1 matching\n", "    print(\"\\n🎯 Step 3: Resolving conflicts for optimal 1:1 matching...\")\n", "    \n", "    # Filter valid matches and sort by score\n", "    valid_matches = [m for m in potential_matches if m['is_valid_match'] and m['best_model_id'] != -1]\n", "    valid_matches.sort(key=lambda x: x['combined_score'], reverse=True)\n", "    \n", "    # Resolve conflicts using Hungarian-like approach (greedy for simplicity)\n", "    used_model_ids = set()\n", "    used_gt_ids = set()\n", "    final_matches = []\n", "    \n", "    for match in valid_matches:\n", "        gt_id = match['gt_id']\n", "        model_id = match['best_model_id']\n", "        \n", "        if gt_id not in used_gt_ids and model_id not in used_model_ids:\n", "            # Create final match object\n", "            final_match = {\n", "                'gt_index': gt_id,\n", "                'model_index': model_id,\n", "                'gt_item': gt_obs[gt_id],\n", "                'model_item': model_obs[model_id],\n", "                'location_similarity': {'similarity_score': match['location_similarity']},\n", "                'observation_similarity': {'similarity_score': match['observation_similarity']},\n", "                'combined_score': match['combined_score'],\n", "                'reasoning': match['reasoning']\n", "            }\n", "            final_matches.append(final_match)\n", "            used_gt_ids.add(gt_id)\n", "            used_model_ids.add(model_id)\n", "    \n", "    print(f\"\\n🎯 Final Results: {len(final_matches)} optimal 1:1 matches found\")\n", "    print(f\"📊 GT Coverage: {len(final_matches)}/{len(gt_obs)} ({len(final_matches)/len(gt_obs)*100:.1f}%)\")\n", "    print(f\"📊 Model Coverage: {len(final_matches)}/{len(model_obs)} ({len(final_matches)/len(model_obs)*100:.1f}%)\")\n", "    \n", "    return final_matches\n", "\n", "# Find optimal matches between observations\n", "observation_matches = find_observation_matches_optimized(gt_observations, model_observations)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Enhanced Analysis Functions\n", "\n", "Analyze heuristics, severity, and false positives/negatives with detailed Gemini analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_heuristic_accuracy_with_gemini(matches: List[Dict]) -> List[Dict]:\n", "    \"\"\"Analyze heuristic accuracy using Gemini for semantic comparison\"\"\"\n", "    print(\"🧠 Analyzing heuristic accuracy using Gemini AI...\")\n", "    \n", "    heuristic_results = []\n", "    \n", "    for i, match in enumerate(matches):\n", "        gt_heuristics = match['gt_item']['heuristics_violated']\n", "        model_heuristics = match['model_item']['heuristics_violated']\n", "        \n", "        print(f\"\\n🔍 Analyzing match {i+1}/{len(matches)}...\")\n", "        \n", "        # Use Gemini to compare heuristic lists\n", "        heuristic_prompt = f\"\"\"\n", "        Compare these two lists of UX heuristics violated for the same issue.\n", "        Analyze semantic similarity and provide detailed comparison.\n", "        \n", "        Ground Truth Heuristics: {gt_heuristics}\n", "        Model Response Heuristics: {model_heuristics}\n", "        \n", "        Context - Issue Location: {match['gt_item']['location']}\n", "        Context - Issue Description: {match['gt_item']['observation'][:200]}...\n", "        \n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"semantic_overlap_score\": <0-100>,\n", "            \"correctly_identified\": [\"list of semantically matching heuristics\"],\n", "            \"missed_heuristics\": [\"list of GT heuristics not captured by model\"],\n", "            \"extra_heuristics\": [\"list of model heuristics not in GT\"],\n", "            \"precision_score\": <0-100>,\n", "            \"recall_score\": <0-100>,\n", "            \"analysis\": \"detailed analysis of the comparison\"\n", "        }}\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = model.generate_content(heuristic_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                heuristic_analysis = json.loads(json_match.group())\n", "                heuristic_analysis['match_index'] = i\n", "                heuristic_analysis['gt_location'] = match['gt_item']['location']\n", "                heuristic_results.append(heuristic_analysis)\n", "                print(f\"✅ Analysis complete - Overlap: {heuristic_analysis['semantic_overlap_score']}%\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in heuristic analysis: {e}\")\n", "    \n", "    return heuristic_results\n", "\n", "def analyze_severity_consistency_with_gemini(matches: List[Dict]) -> List[Dict]:\n", "    \"\"\"Analyze severity consistency using Gemini\"\"\"\n", "    print(\"⚖️ Analyzing severity consistency using Gemini AI...\")\n", "    \n", "    severity_results = []\n", "    \n", "    for i, match in enumerate(matches):\n", "        gt_severity = match['gt_item']['severity']\n", "        model_severity = match['model_item']['severity']\n", "        \n", "        print(f\"\\n🔍 Analyzing severity for match {i+1}/{len(matches)}...\")\n", "        \n", "        # Use Gemini to analyze severity appropriateness\n", "        severity_prompt = f\"\"\"\n", "        Analyze the severity rating consistency for this UX issue.\n", "        \n", "        Issue Location: {match['gt_item']['location']}\n", "        Issue Description: {match['gt_item']['observation']}\n", "        \n", "        Ground Truth Severity: {gt_severity}\n", "        Model Response Severity: {model_severity}\n", "        \n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"severity_match\": <true/false>,\n", "            \"severity_appropriateness_score\": <0-100>,\n", "            \"gt_severity_justified\": <true/false>,\n", "            \"model_severity_justified\": <true/false>,\n", "            \"severity_gap_analysis\": \"explanation of any severity differences\",\n", "            \"recommended_severity\": \"High/Medium/Low\",\n", "            \"analysis\": \"detailed analysis of severity consistency\"\n", "        }}\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = model.generate_content(severity_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                severity_analysis = json.loads(json_match.group())\n", "                severity_analysis['match_index'] = i\n", "                severity_analysis['gt_severity'] = gt_severity\n", "                severity_analysis['model_severity'] = model_severity\n", "                severity_analysis['gt_location'] = match['gt_item']['location']\n", "                severity_results.append(severity_analysis)\n", "                print(f\"✅ Severity analysis complete - Match: {severity_analysis['severity_match']}\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in severity analysis: {e}\")\n", "    \n", "    return severity_results\n", "\n", "def analyze_false_positives_negatives_with_gemini(gt_obs: List[Dict], model_obs: List[Dict], matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze false positives and negatives using Gemini\"\"\"\n", "    print(\"🔍 Analyzing false positives and negatives using Gemini AI...\")\n", "    \n", "    # Get matched indices\n", "    matched_gt_indices = {match['gt_index'] for match in matches}\n", "    matched_model_indices = {match['model_index'] for match in matches}\n", "    \n", "    # False negatives: GT observations not matched\n", "    false_negatives = [obs for i, obs in enumerate(gt_obs) if i not in matched_gt_indices]\n", "    \n", "    # False positives: Model observations not matched\n", "    false_positives = [obs for i, obs in enumerate(model_obs) if i not in matched_model_indices]\n", "    \n", "    print(f\"\\n📊 Found {len(false_negatives)} false negatives (missed GT issues)\")\n", "    print(f\"📊 Found {len(false_positives)} false positives (model hallucinations)\")\n", "    \n", "    # Analyze false negatives with <PERSON>\n", "    fn_analysis = []\n", "    if false_negatives:\n", "        print(\"\\n🔍 Analyzing false negatives...\")\n", "        for i, fn in enumerate(false_negatives):\n", "            fn_prompt = f\"\"\"\n", "            Analyze why this ground truth UX issue might have been missed by the model.\n", "            \n", "            Missed Issue:\n", "            Location: {fn['location']}\n", "            Severity: {fn['severity']}\n", "            Observation: {fn['observation']}\n", "            Heuristics: {fn['heuristics_violated']}\n", "            \n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"issue_complexity\": <1-10>,\n", "                \"issue_visibility\": <1-10>,\n", "                \"likely_reasons_missed\": [\"list of reasons\"],\n", "                \"impact_of_missing\": \"High/Medium/Low\",\n", "                \"analysis\": \"detailed analysis of why this was missed\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(fn_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fn_result = json.loads(json_match.group())\n", "                    fn_result['gt_item'] = fn\n", "                    fn_analysis.append(fn_result)\n", "                    print(f\"✅ FN analysis {i+1}/{len(false_negatives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FN {i+1}: {e}\")\n", "    \n", "    # Analyze false positives with <PERSON>\n", "    fp_analysis = []\n", "    if false_positives:\n", "        print(\"\\n🔍 Analyzing false positives...\")\n", "        for i, fp in enumerate(false_positives):\n", "            fp_prompt = f\"\"\"\n", "            Analyze whether this model-generated UX issue is a valid concern or a hallucination.\n", "            \n", "            Model Issue:\n", "            Location: {fp['location']}\n", "            Severity: {fp['severity']}\n", "            Observation: {fp['observation']}\n", "            Heuristics: {fp['heuristics_violated']}\n", "            \n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"is_valid_issue\": <true/false>,\n", "                \"validity_confidence\": <0-100>,\n", "                \"issue_quality\": <1-10>,\n", "                \"potential_value\": \"High/Medium/Low\",\n", "                \"classification\": \"Valid Addition/Minor Issue/Hallucination\",\n", "                \"analysis\": \"detailed analysis of the issue validity\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(fp_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fp_result = json.loads(json_match.group())\n", "                    fp_result['model_item'] = fp\n", "                    fp_analysis.append(fp_result)\n", "                    print(f\"✅ FP analysis {i+1}/{len(false_positives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FP {i+1}: {e}\")\n", "    \n", "    return {\n", "        'false_negatives': false_negatives,\n", "        'false_positives': false_positives,\n", "        'fn_analysis': fn_analysis,\n", "        'fp_analysis': fp_analysis\n", "    }\n", "\n", "# Run all analysis functions\n", "print(\"\\n🚀 Starting comprehensive analysis...\")\n", "heuristic_analysis = analyze_heuristic_accuracy_with_gemini(observation_matches)\n", "severity_analysis = analyze_severity_consistency_with_gemini(observation_matches)\n", "fp_fn_analysis = analyze_false_positives_negatives_with_gemini(gt_observations, model_observations, observation_matches)\n", "print(\"\\n✅ All analysis complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Enhanced CSV Creation with Detailed Descriptions\n", "\n", "Create user-friendly CSV with clear, detailed descriptions for location, observation, and heuristic analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_enhanced_evaluation_dataframe(gt_obs, model_obs, matches, heuristic_analysis, severity_analysis, fp_fn_analysis):\n", "    \"\"\"Create enhanced DataFrame with detailed descriptions using Gemini\"\"\"\n", "    print(\"📊 Creating enhanced evaluation DataFrame with detailed descriptions...\")\n", "    \n", "    all_data = []\n", "    \n", "    # 1. MATCHED OBSERVATIONS with Enhanced Descriptions\n", "    for i, match in enumerate(matches):\n", "        gt_item = match['gt_item']\n", "        model_item = match['model_item']\n", "        \n", "        print(f\"\\n🔍 Processing match {i+1}/{len(matches)} with enhanced descriptions...\")\n", "        \n", "        # Get analysis data\n", "        heuristic_data = next((h for h in heuristic_analysis if h.get('match_index') == i), {})\n", "        severity_data = next((s for s in severity_analysis if s.get('match_index') == i), {})\n", "        \n", "        # Enhanced Location Analysis with Gemini\n", "        location_score = match['location_similarity']['similarity_score']\n", "        location_analysis_prompt = f\"\"\"\n", "        Compare these two UX issue locations and provide a clear, detailed analysis:\n", "        \n", "        Ground Truth Location: \"{gt_item['location']}\"\n", "        Model Response Location: \"{model_item['location']}\"\n", "        \n", "        Provide analysis in this format:\n", "        \"SIMILARITY: {location_score:.1f}% - Both locations refer to [common elements]. SAME CONTEXT: [what is same]. DIFFERENCES: GT mentions [specific GT elements] while <PERSON> mentions [specific Model elements]. MISSING IN MODEL: [what GT has that Model lacks]. EXTRA IN MODEL: [what Model has that GT lacks].\"\n", "        \n", "        Keep it concise but clear for easy understanding.\n", "        \"\"\"\n", "        \n", "        try:\n", "            location_response = model.generate_content(location_analysis_prompt)\n", "            location_description = location_response.text.strip()\n", "        except:\n", "            location_description = f\"SIMILARITY: {location_score:.1f}% - Both locations refer to similar UI areas. Manual analysis needed for detailed comparison.\"\n", "        \n", "        # Enhanced Observation Analysis with Gemini\n", "        observation_score = match['observation_similarity']['similarity_score']\n", "        gt_word_count = len(gt_item['observation'].split())\n", "        model_word_count = len(model_item['observation'].split())\n", "        gt_depth = \"High\" if gt_word_count > 50 else \"Medium\" if gt_word_count > 25 else \"Low\"\n", "        model_depth = \"High\" if model_word_count > 50 else \"Medium\" if model_word_count > 25 else \"Low\"\n", "        \n", "        observation_analysis_prompt = f\"\"\"\n", "        Compare these two UX observations and provide a clear, detailed analysis:\n", "        \n", "        Ground Truth Observation: \"{gt_item['observation']}\"\n", "        Model Response Observation: \"{model_item['observation']}\"\n", "        \n", "        Provide analysis in this format:\n", "        \"SIMILARITY: {observation_score:.1f}% - DEPTH: GT has {gt_word_count} words ({gt_depth} detail), Model has {model_word_count} words ({model_depth} detail). SAME CONTEXT: [what core issues both identify]. MISSING IN MODEL: [specific points/details GT mentions that <PERSON> lacks]. EXTRA IN MODEL: [specific points/details Model mentions that GT lacks]. DEPTH COMPARISON: [which provides more comprehensive analysis and why].\"\n", "        \n", "        Focus on UX insights, technical details, and user impact mentioned in each.\n", "        \"\"\"\n", "        \n", "        try:\n", "            observation_response = model.generate_content(observation_analysis_prompt)\n", "            observation_description = observation_response.text.strip()\n", "        except:\n", "            observation_description = f\"SIMILARITY: {observation_score:.1f}% - DEPTH: GT has {gt_word_count} words ({gt_depth} detail), Model has {model_word_count} words ({model_depth} detail). Both identify similar UX issues. Manual analysis needed for detailed comparison.\"\n", "        \n", "        # Coverage score (equal weight for location and observation)\n", "        coverage_score = (location_score + observation_score) / 2\n", "        \n", "        # Enhanced Heuristic Analysis\n", "        gt_heuristics = gt_item['heuristics_violated']\n", "        model_heuristics = model_item['heuristics_violated']\n", "        matched_heuristics = heuristic_data.get('correctly_identified', [])\n", "        missed_heuristics = heuristic_data.get('missed_heuristics', [])\n", "        extra_heuristics = heuristic_data.get('extra_heuristics', [])\n", "        \n", "        total_gt_heuristics = len(gt_heuristics)\n", "        total_model_heuristics = len(model_heuristics)\n", "        matched_count = len(matched_heuristics)\n", "        \n", "        # Enhanced heuristic description\n", "        heuristic_score_text = f\"{matched_count}/{total_gt_heuristics} GT heuristics matched\"\n", "        heuristic_percentage = (matched_count / total_gt_heuristics * 100) if total_gt_heuristics > 0 else 0\n", "        \n", "        # Create detailed heuristic description\n", "        heuristic_description = f\"TOTAL GT HEURISTICS: {total_gt_heuristics} | TOTAL MODEL HEURISTICS: {total_model_heuristics} | MATCHED: {matched_count} ({'; '.join(matched_heuristics) if matched_heuristics else 'None'}) | MISSED BY MODEL: {'; '.join(missed_heuristics) if missed_heuristics else 'None'} | EXTRA IN MODEL: {'; '.join(extra_heuristics) if extra_heuristics else 'None'} | COVERAGE: {heuristic_percentage:.1f}%\"\n", "        \n", "        # Severity scoring\n", "        severity_match = severity_data.get('severity_match', False)\n", "        severity_score = 100 if severity_match else severity_data.get('severity_appropriateness_score', 50)\n", "        \n", "        # Overall score with specified weights (Location 30%, Observation 30%, Heuristic 25%, Severity 15%)\n", "        overall_score = (\n", "            location_score * 0.30 +\n", "            observation_score * 0.30 +\n", "            heuristic_percentage * 0.25 +\n", "            severity_score * 0.15\n", "        )\n", "        \n", "        row_data = {\n", "            'GT_ID': gt_item['id'],\n", "            'Model_Response_ID': model_item['id'],\n", "            'Match_Type': 'Matched',\n", "            \n", "            # Location Analysis (30% weight)\n", "            'GT_Location': gt_item['location'],\n", "            'Model_Location': model_item['location'],\n", "            'Location_Score_Percentage': f\"{location_score:.1f}%\",\n", "            'Location_Description': location_description,\n", "            \n", "            # Observation Analysis (30% weight)\n", "            'GT_Observation': gt_item['observation'],\n", "            'Model_Observation': model_item['observation'],\n", "            'Observation_Score_Percentage': f\"{observation_score:.1f}%\",\n", "            'Observation_Description': observation_description,\n", "            \n", "            # Coverage Score (Location + Observation combined)\n", "            'Coverage_Score_Percentage': f\"{coverage_score:.1f}%\",\n", "            \n", "            # Heuristic Analysis (25% weight)\n", "            'Heuristic_Match_Score': heuristic_score_text,\n", "            'Heuristic_Percentage': f\"{heuristic_percentage:.1f}%\",\n", "            'Matched_Heuristics': '; '.join(matched_heuristics) if matched_heuristics else 'None',\n", "            'Unmatched_Heuristics': '; '.join(missed_heuristics) if missed_heuristics else 'None',\n", "            'Heuristic_Description': heuristic_description,\n", "            \n", "            # Severity Analysis (15% weight)\n", "            'GT_Severity': gt_item['severity'],\n", "            'Model_Severity': model_item['severity'],\n", "            'Severity_Score_Percentage': f\"{severity_score:.1f}%\",\n", "            'Severity_Match': 'Yes' if severity_match else 'No',\n", "            \n", "            # Overall Score\n", "            'Overall_Score_Percentage': f\"{overall_score:.1f}%\",\n", "            'Performance_Grade': (\n", "                'Excellent (A)' if overall_score >= 85 else\n", "                'Good (B)' if overall_score >= 70 else\n", "                'Fair (C)' if overall_score >= 55 else\n", "                'Poor (D)' if overall_score >= 40 else\n", "                'Very Poor (F)'\n", "            )\n", "        }\n", "        \n", "        all_data.append(row_data)\n", "    \n", "    return all_data\n", "\n", "# Create enhanced data for matched observations\n", "enhanced_matched_data = create_enhanced_evaluation_dataframe(\n", "    gt_observations, model_observations, observation_matches, \n", "    heuristic_analysis, severity_analysis, fp_fn_analysis\n", ")\n", "\n", "print(f\"\\n✅ Enhanced matched data created: {len(enhanced_matched_data)} records\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add False Negatives and False Positives to the data\n", "print(\"\\n📊 Adding False Negatives and False Positives...\")\n", "\n", "all_evaluation_data = enhanced_matched_data.copy()\n", "\n", "# Add False Negatives (Missed GT Issues)\n", "for fn_item in fp_fn_analysis['false_negatives']:\n", "    fn_analysis_data = next((fn for fn in fp_fn_analysis['fn_analysis'] if fn.get('gt_item', {}).get('id') == fn_item['id']), {})\n", "    \n", "    fn_row = {\n", "        'GT_ID': fn_item['id'],\n", "        'Model_Response_ID': 'MISSED',\n", "        'Match_Type': 'False Negative (Missed by Model)',\n", "        'GT_Location': fn_item['location'],\n", "        'Model_Location': 'N/A - Issue was completely missed',\n", "        'Location_Score_Percentage': '0%',\n", "        'Location_Description': f\"MISSED ISSUE: GT location '{fn_item['location']}' was not identified by the model. This represents a complete miss of the UX issue.\",\n", "        'GT_Observation': fn_item['observation'],\n", "        'Model_Observation': 'N/A - Issue was completely missed',\n", "        'Observation_Score_Percentage': '0%',\n", "        'Observation_Description': f\"MISSED OBSERVATION: GT has {len(fn_item['observation'].split())} words describing the UX issue. Model completely missed this issue. Impact: {fn_analysis_data.get('impact_of_missing', 'Unknown')}. Likely reasons: {'; '.join(fn_analysis_data.get('likely_reasons_missed', ['Unknown']))}.\",\n", "        'Coverage_Score_Percentage': '0%',\n", "        'Heuristic_Match_Score': f\"0/{len(fn_item['heuristics_violated'])} GT heuristics matched\",\n", "        'Heuristic_Percentage': '0%',\n", "        'Matched_Heuristics': 'None',\n", "        'Unmatched_Heuristics': '; '.join(fn_item['heuristics_violated']),\n", "        'Heuristic_Description': f\"TOTAL GT HEURISTICS: {len(fn_item['heuristics_violated'])} | TOTAL MODEL HEURISTICS: 0 | MATCHED: 0 (None) | MISSED BY MODEL: {'; '.join(fn_item['heuristics_violated'])} | EXTRA IN MODEL: None | COVERAGE: 0% - All heuristics missed as issue was not identified\",\n", "        'GT_Severity': fn_item['severity'],\n", "        'Model_Severity': 'N/A - Issue was completely missed',\n", "        'Severity_Score_Percentage': '0%',\n", "        'Severity_Match': 'No',\n", "        'Overall_Score_Percentage': '0%',\n", "        'Performance_Grade': 'Missed (F)'\n", "    }\n", "    all_evaluation_data.append(fn_row)\n", "\n", "# Add False Positives (Model Additions)\n", "for fp_item in fp_fn_analysis['false_positives']:\n", "    fp_analysis_data = next((fp for fp in fp_fn_analysis['fp_analysis'] if fp.get('model_item', {}).get('id') == fp_item['id']), {})\n", "    \n", "    validity = fp_analysis_data.get('is_valid_issue', False)\n", "    quality_score = fp_analysis_data.get('issue_quality', 0) * 10  # Convert to percentage\n", "    classification = fp_analysis_data.get('classification', 'Unknown')\n", "    \n", "    fp_row = {\n", "        'GT_ID': 'ADDED',\n", "        'Model_Response_ID': fp_item['id'],\n", "        'Match_Type': 'False Positive (Added by Model)',\n", "        'GT_Location': 'N/A - Additional issue identified by model',\n", "        'Model_Location': fp_item['location'],\n", "        'Location_Score_Percentage': 'N/A',\n", "        'Location_Description': f\"ADDITIONAL ISSUE: Model identified new issue at '{fp_item['location']}'. Validity: {'Valid' if validity else 'Invalid'}. Classification: {classification}.\",\n", "        'GT_Observation': 'N/A - Additional issue identified by model',\n", "        'Model_Observation': fp_item['observation'],\n", "        'Observation_Score_Percentage': 'N/A',\n", "        'Observation_Description': f\"ADDITIONAL OBSERVATION: Model generated {len(fp_item['observation'].split())} words describing this issue. Validity: {'Valid' if validity else 'Invalid'}. Quality Score: {quality_score:.1f}%. Classification: {classification}. Analysis: {fp_analysis_data.get('analysis', 'No detailed analysis available')}.\",\n", "        'Coverage_Score_Percentage': 'N/A',\n", "        'Heuristic_Match_Score': f\"N/A ({len(fp_item['heuristics_violated'])} identified by model)\",\n", "        'Heuristic_Percentage': 'N/A',\n", "        'Matched_Heuristics': 'N/A',\n", "        'Unmatched_Heuristics': 'N/A',\n", "        'Heuristic_Description': f\"TOTAL GT HEURISTICS: N/A | TOTAL MODEL HEURISTICS: {len(fp_item['heuristics_violated'])} | MODEL IDENTIFIED: {'; '.join(fp_item['heuristics_violated'])} | VALIDITY: {'Valid' if validity else 'Invalid'} | CLASSIFICATION: {classification}\",\n", "        'GT_Severity': 'N/A - Additional issue identified by model',\n", "        'Model_Severity': fp_item['severity'],\n", "        'Severity_Score_Percentage': 'N/A',\n", "        'Severity_Match': 'N/A',\n", "        'Overall_Score_Percentage': f\"{quality_score:.1f}%\" if validity else '0%',\n", "        'Performance_Grade': f\"Valid Addition ({classification})\" if validity else f\"Invalid Addition ({classification})\"\n", "    }\n", "    all_evaluation_data.append(fp_row)\n", "\n", "# Create final DataFrame\n", "evaluation_df = pd.DataFrame(all_evaluation_data)\n", "\n", "print(f\"\\n✅ Complete evaluation DataFrame created!\")\n", "print(f\"📊 Total Records: {len(evaluation_df)}\")\n", "print(f\"   • Matched: {len([d for d in all_evaluation_data if d['Match_Type'] == 'Matched'])}\")\n", "print(f\"   • False Negatives: {len([d for d in all_evaluation_data if 'False Negative' in d['Match_Type']])}\")\n", "print(f\"   • False Positives: {len([d for d in all_evaluation_data if 'False Positive' in d['Match_Type']])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Export Enhanced CSV with Detailed Analysis\n", "\n", "Export the comprehensive evaluation results to a user-friendly CSV file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export the enhanced evaluation DataFrame to CSV\n", "timestamp = pd.Timestamp.now().strftime(\"%Y%m%d_%H%M%S\")\n", "csv_filename = f'ux_audit_evaluation_enhanced_{timestamp}.csv'\n", "\n", "try:\n", "    # Export the enhanced evaluation DataFrame\n", "    evaluation_df.to_csv(csv_filename, index=False, encoding='utf-8')\n", "    print(f\"✅ Enhanced evaluation results exported to: {csv_filename}\")\n", "    \n", "    # Calculate and display final metrics\n", "    matched_records = evaluation_df[evaluation_df['Match_Type'] == 'Matched']\n", "    total_gt = len(gt_observations)\n", "    total_model = len(model_observations)\n", "    true_positives = len(matched_records)\n", "    false_negatives = len(evaluation_df[evaluation_df['Match_Type'].str.contains('False Negative', na=False)])\n", "    false_positives = len(evaluation_df[evaluation_df['Match_Type'].str.contains('False Positive', na=False)])\n", "    \n", "    # Calculate metrics\n", "    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0\n", "    recall = true_positives / total_gt if total_gt > 0 else 0\n", "    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "    \n", "    # Average scores for matched records\n", "    if not matched_records.empty:\n", "        avg_overall_score = matched_records['Overall_Score_Percentage'].str.rstrip('%').astype(float).mean()\n", "        avg_location_score = matched_records['Location_Score_Percentage'].str.rstrip('%').astype(float).mean()\n", "        avg_observation_score = matched_records['Observation_Score_Percentage'].str.rstrip('%').astype(float).mean()\n", "        avg_heuristic_score = matched_records['Heuristic_Percentage'].str.rstrip('%').astype(float).mean()\n", "        avg_severity_score = matched_records['Severity_Score_Percentage'].str.rstrip('%').astype(float).mean()\n", "    else:\n", "        avg_overall_score = avg_location_score = avg_observation_score = avg_heuristic_score = avg_severity_score = 0\n", "    \n", "    # Overall model performance score with specified weights\n", "    overall_model_score = (\n", "        avg_location_score * 0.30 + \n", "        avg_observation_score * 0.30 + \n", "        avg_heuristic_score * 0.25 + \n", "        avg_severity_score * 0.15\n", "    ) * (recall)  # Multiply by recall to account for coverage\n", "    \n", "    performance_grade = (\n", "        'A+' if overall_model_score >= 90 else\n", "        'A' if overall_model_score >= 85 else\n", "        'A-' if overall_model_score >= 80 else\n", "        'B+' if overall_model_score >= 75 else\n", "        'B' if overall_model_score >= 70 else\n", "        'B-' if overall_model_score >= 65 else\n", "        'C+' if overall_model_score >= 60 else\n", "        'C' if overall_model_score >= 55 else\n", "        'C-' if overall_model_score >= 50 else\n", "        'D' if overall_model_score >= 40 else\n", "        'F'\n", "    )\n", "    \n", "    print(\"\\n🎯 ENHANCED EVALUATION RESULTS\")\n", "    print(\"=\" * 60)\n", "    print(f\"📊 Total GT Issues: {total_gt}\")\n", "    print(f\"📊 Total Model Issues: {total_model}\")\n", "    print(f\"✅ True Positives (Matched): {true_positives}\")\n", "    print(f\"❌ False Negatives (Missed): {false_negatives}\")\n", "    print(f\"➕ False Positives (Added): {false_positives}\")\n", "    print(f\"\\n📈 PERFORMANCE METRICS:\")\n", "    print(f\"   • Precision: {precision:.3f} ({precision*100:.1f}%)\")\n", "    print(f\"   • Recall (Coverage): {recall:.3f} ({recall*100:.1f}%)\")\n", "    print(f\"   • F1-Score: {f1_score:.3f} ({f1_score*100:.1f}%)\")\n", "    print(f\"\\n📊 AVERAGE SCORES (Matched Records Only):\")\n", "    print(f\"   • Overall Score: {avg_overall_score:.1f}%\")\n", "    print(f\"   • Location Score: {avg_location_score:.1f}%\")\n", "    print(f\"   • Observation Score: {avg_observation_score:.1f}%\")\n", "    print(f\"   • Heuristic Score: {avg_heuristic_score:.1f}%\")\n", "    print(f\"   • Severity Score: {avg_severity_score:.1f}%\")\n", "    print(f\"\\n🏆 OVERALL MODEL SCORE: {overall_model_score:.1f}% (Grade: {performance_grade})\")\n", "    print(f\"\\n📋 Weights Used: Location 30%, Observation 30%, Heuristic 25%, Severity 15%\")\n", "    \n", "    print(\"\\n📋 ENHANCED CSV FEATURES:\")\n", "    print(\"=\" * 40)\n", "    print(\"✅ Clear Location Descriptions - What's same, missing, or extra\")\n", "    print(\"✅ Detailed Observation Analysis - Depth comparison with specific points\")\n", "    print(\"✅ Enhanced Heuristic Matching - Clear count and detailed breakdown\")\n", "    print(\"✅ Comprehensive Coverage Analysis - All evaluation parameters included\")\n", "    print(\"✅ User-Friendly Format - Easy to understand for any person\")\n", "    \n", "    print(f\"\\n📁 CSV file saved: {csv_filename}\")\n", "    print(f\"📊 Total records: {len(evaluation_df)}\")\n", "    \n", "    print(\"\\n🎉 ENHANCED UX AUDIT EVALUATION COMPLETE!\")\n", "    print(\"=\" * 60)\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error exporting CSV file: {e}\")\n", "    print(\"Please check file permissions and try again.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}