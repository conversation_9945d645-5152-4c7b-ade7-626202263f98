{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UX Audit Report Evaluation Using Gemini AI\n", "\n", "This notebook evaluates model-generated UX audit reports against ground truth data using Google's Gemini AI model.\n", "\n", "## Evaluation Parameters:\n", "1. **Observation Coverage** - How many GT observations were matched by the model\n", "2. **Heuristic Match Accuracy** - Precision, recall, and F1-score for heuristics\n", "3. **Severity Consistency** - Comparison of severity ratings\n", "4. **Location Accuracy** - Semantic similarity of issue locations\n", "5. **Observation Similarity** - Semantic comparison of observation texts\n", "6. **False Positives/Hallucinations** - Issues invented by the model\n", "7. **False Negatives/Omissions** - GT issues missed by the model\n", "8. **Overall Evaluation Summary** - Comprehensive analysis by Gemini"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-generativeai pandas numpy mat<PERSON><PERSON><PERSON>b seaborn scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Any\n", "import google.generativeai as genai\n", "from sklearn.metrics import precision_recall_fscore_support\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure Gemini API\n", "# Replace 'YOUR_API_KEY' with your actual Gemini API key\n", "GEMINI_API_KEY = 'YOUR_API_KEY'  # Get from https://makersuite.google.com/app/apikey\n", "genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "# Initialize Gemini model\n", "model = genai.GenerativeModel('gemini-1.5-flash')\n", "\n", "print(\"✅ Gemini AI configured successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data files\n", "def load_json_data(file_path: str) -> Dict:\n", "    \"\"\"Load JSON data from file\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return json.load(f)\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return {}\n", "\n", "# Load ground truth and model response data\n", "ground_truth = load_json_data('ground_truth.json')\n", "model_response = load_json_data('model_response.json')\n", "\n", "# Extract observations\n", "gt_observations = ground_truth.get('audit_report', {}).get('observations', [])\n", "model_observations = model_response.get('audit_report', {}).get('observations', [])\n", "\n", "print(f\"📊 Loaded {len(gt_observations)} ground truth observations\")\n", "print(f\"📊 Loaded {len(model_observations)} model response observations\")\n", "print(\"\\n✅ Data loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Location and Observation Matching Using Gemini\n", "\n", "We use Gemini to semantically match observations between ground truth and model response based on location similarity and observation content overlap."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_gemini_similarity_score(text1: str, text2: str, context: str = \"\") -> Dict:\n", "    \"\"\"Get semantic similarity score and analysis from Gemini\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two UX audit observations and provide:\n", "    1. Similarity score (0-100)\n", "    2. Brief analysis of similarities and differences\n", "    3. Whether they refer to the same UX issue (yes/no)\n", "    \n", "    Context: {context}\n", "    \n", "    Text 1: {text1}\n", "    Text 2: {text2}\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"similarity_score\": <0-100>,\n", "        \"same_issue\": <true/false>,\n", "        \"analysis\": \"<brief analysis>\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error in Gemini API call: {e}\")\n", "        return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": f\"API Error: {e}\"}\n", "\n", "print(\"🔧 Gemini similarity function ready!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_observation_matches_optimized(gt_obs: List[Dict], model_obs: List[Dict]) -> List[Dict]:\n", "    \"\"\"Find optimal 1:1 matches between GT and model observations using Gemini efficiently\"\"\"\n", "    print(\"🔍 Finding optimal observation matches using Gemini AI...\")\n", "    print(f\"📊 Processing {len(gt_obs)} GT observations vs {len(model_obs)} model observations\")\n", "    \n", "    # Step 1: Batch similarity analysis for all pairs\n", "    print(\"\\n🚀 Step 1: <PERSON><PERSON> analyzing all GT-Model pairs...\")\n", "    \n", "    similarity_matrix = []\n", "    \n", "    # Create batch prompt for all comparisons to reduce API calls\n", "    batch_prompt = f\"\"\"\n", "    Compare these UX audit observations and provide similarity scores for each pair.\n", "    For each comparison, consider both location similarity and observation content similarity.\n", "    \n", "    GROUND TRUTH OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": gt[\"location\"], \"observation\": gt[\"observation\"][:200] + \"...\"} for i, gt in enumerate(gt_obs)], indent=2)}\n", "    \n", "    MODEL RESPONSE OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": model[\"location\"], \"observation\": model[\"observation\"][:200] + \"...\"} for i, model in enumerate(model_obs)], indent=2)}\n", "    \n", "    For each GT observation, find the best matching model observation and provide:\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"matches\": [\n", "            {{\n", "                \"gt_id\": <gt_index>,\n", "                \"best_model_id\": <model_index or -1 if no good match>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "        ]\n", "    }}\n", "    \n", "    Only consider matches with combined_score > 40. Set best_model_id to -1 if no good match exists.\n", "    \"\"\"\n", "    \n", "    try:\n", "        print(\"🤖 Sending batch request to Gemini...\")\n", "        response = model.generate_content(batch_prompt)\n", "        \n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            batch_results = json.loads(json_match.group())\n", "            potential_matches = batch_results.get('matches', [])\n", "            print(f\"✅ Batch analysis complete! Found {len(potential_matches)} potential matches\")\n", "        else:\n", "            print(\"❌ Failed to parse batch response, falling back to individual analysis\")\n", "            potential_matches = []\n", "    except Exception as e:\n", "        print(f\"❌ Batch analysis failed: {e}\")\n", "        print(\"🔄 Falling back to optimized individual analysis...\")\n", "        potential_matches = []\n", "    \n", "    # Step 2: If batch failed, use optimized individual analysis\n", "    if not potential_matches:\n", "        potential_matches = []\n", "        for i, gt_item in enumerate(gt_obs):\n", "            print(f\"\\n📍 Processing GT {i+1}/{len(gt_obs)}: {gt_item['location'][:30]}...\")\n", "            \n", "            # Quick location-based filtering first\n", "            candidate_models = []\n", "            for j, model_item in enumerate(model_obs):\n", "                # Simple keyword overlap check for initial filtering\n", "                gt_keywords = set(gt_item['location'].lower().split())\n", "                model_keywords = set(model_item['location'].lower().split())\n", "                keyword_overlap = len(gt_keywords.intersection(model_keywords)) / max(len(gt_keywords), 1)\n", "                \n", "                if keyword_overlap > 0.2:  # At least 20% keyword overlap\n", "                    candidate_models.append((j, model_item, keyword_overlap))\n", "            \n", "            # Sort by keyword overlap and take top 3 candidates\n", "            candidate_models.sort(key=lambda x: x[2], reverse=True)\n", "            top_candidates = candidate_models[:3]\n", "            \n", "            if not top_candidates:\n", "                print(f\"  ❌ No location candidates found\")\n", "                potential_matches.append({\n", "                    'gt_id': i,\n", "                    'best_model_id': -1,\n", "                    'combined_score': 0,\n", "                    'is_valid_match': <PERSON><PERSON><PERSON>,\n", "                    'reasoning': 'No location similarity found'\n", "                })\n", "                continue\n", "            \n", "            # Use Gemini only for top candidates\n", "            best_match = None\n", "            best_score = 0\n", "            \n", "            candidates_text = \"\\n\".join([f\"Model {idx}: {item['location']} - {item['observation'][:100]}...\" \n", "                                        for idx, item, _ in top_candidates])\n", "            \n", "            candidate_prompt = f\"\"\"\n", "            Find the best match for this GT observation among the candidates:\n", "            \n", "            GT Observation:\n", "            Location: {gt_item['location']}\n", "            Description: {gt_item['observation'][:200]}...\n", "            \n", "            Candidates:\n", "            {candidates_text}\n", "            \n", "            Respond in JSON format:\n", "            {{\n", "                \"best_model_id\": <model_index or -1>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(candidate_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    result = json.loads(json_match.group())\n", "                    result['gt_id'] = i\n", "                    potential_matches.append(result)\n", "                    if result['is_valid_match']:\n", "                        print(f\"  ✅ Match found: Model {result['best_model_id']} (Score: {result['combined_score']})\")\n", "                    else:\n", "                        print(f\"  ❌ No valid match (Best score: {result['combined_score']})\")\n", "                else:\n", "                    print(f\"  ❌ Failed to parse response\")\n", "            except Exception as e:\n", "                print(f\"  ❌ Error: {e}\")\n", "    \n", "    # Step 3: Resolve conflicts and create optimal 1:1 matching\n", "    print(\"\\n🎯 Step 3: Resolving conflicts for optimal 1:1 matching...\")\n", "    \n", "    # Filter valid matches and sort by score\n", "    valid_matches = [m for m in potential_matches if m['is_valid_match'] and m['best_model_id'] != -1]\n", "    valid_matches.sort(key=lambda x: x['combined_score'], reverse=True)\n", "    \n", "    # Resolve conflicts using Hungarian-like approach (greedy for simplicity)\n", "    used_model_ids = set()\n", "    used_gt_ids = set()\n", "    final_matches = []\n", "    \n", "    for match in valid_matches:\n", "        gt_id = match['gt_id']\n", "        model_id = match['best_model_id']\n", "        \n", "        if gt_id not in used_gt_ids and model_id not in used_model_ids:\n", "            # Create final match object\n", "            final_match = {\n", "                'gt_index': gt_id,\n", "                'model_index': model_id,\n", "                'gt_item': gt_obs[gt_id],\n", "                'model_item': model_obs[model_id],\n", "                'location_similarity': {'similarity_score': match['location_similarity']},\n", "                'observation_similarity': {'similarity_score': match['observation_similarity']},\n", "                'combined_score': match['combined_score'],\n", "                'reasoning': match['reasoning']\n", "            }\n", "            final_matches.append(final_match)\n", "            used_gt_ids.add(gt_id)\n", "            used_model_ids.add(model_id)\n", "    \n", "    print(f\"\\n🎯 Final Results: {len(final_matches)} optimal 1:1 matches found\")\n", "    print(f\"📊 GT Coverage: {len(final_matches)}/{len(gt_obs)} ({len(final_matches)/len(gt_obs)*100:.1f}%)\")\n", "    print(f\"📊 Model Coverage: {len(final_matches)}/{len(model_obs)} ({len(final_matches)/len(model_obs)*100:.1f}%)\")\n", "    \n", "    return final_matches\n", "\n", "# Find optimal matches between observations\n", "observation_matches = find_observation_matches_optimized(gt_observations, model_observations)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Observation Coverage Analysis\n", "\n", "Calculate how many ground truth observations were successfully matched by the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_observation_coverage(matches: List[Dict], total_gt: int) -> Dict:\n", "    \"\"\"Calculate observation coverage metrics\"\"\"\n", "    coverage_score = len(matches) / total_gt if total_gt > 0 else 0\n", "    \n", "    coverage_analysis = {\n", "        'total_gt_observations': total_gt,\n", "        'matched_observations': len(matches),\n", "        'coverage_percentage': coverage_score * 100,\n", "        'unmatched_observations': total_gt - len(matches)\n", "    }\n", "    \n", "    return coverage_analysis\n", "\n", "# Calculate coverage\n", "coverage_results = calculate_observation_coverage(observation_matches, len(gt_observations))\n", "\n", "print(\"📊 OBSERVATION COVERAGE ANALYSIS\")\n", "print(\"=\" * 40)\n", "print(f\"Total Ground Truth Observations: {coverage_results['total_gt_observations']}\")\n", "print(f\"Successfully Matched: {coverage_results['matched_observations']}\")\n", "print(f\"Coverage Percentage: {coverage_results['coverage_percentage']:.1f}%\")\n", "print(f\"Unmatched Observations: {coverage_results['unmatched_observations']}\")\n", "\n", "# Visualize coverage\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Coverage pie chart\n", "labels = ['Matched', 'Unmatched']\n", "sizes = [coverage_results['matched_observations'], coverage_results['unmatched_observations']]\n", "colors = ['#2ecc71', '#e74c3c']\n", "ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)\n", "ax1.set_title('Observation Coverage', fontsize=14, fontweight='bold')\n", "\n", "# Coverage bar chart\n", "categories = ['Coverage %', 'Missing %']\n", "values = [coverage_results['coverage_percentage'], 100 - coverage_results['coverage_percentage']]\n", "bars = ax2.bar(categories, values, color=colors)\n", "ax2.set_ylabel('Percentage')\n", "ax2.set_title('Coverage vs Missing Observations', fontsize=14, fontweight='bold')\n", "ax2.set_ylim(0, 100)\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, values):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "             f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Heuristic Match Accuracy Analysis\n", "\n", "For each matched observation, analyze how well the model identified the violated heuristics using Gemini."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_heuristic_accuracy_with_gemini(matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze heuristic accuracy using Gemini for semantic comparison\"\"\"\n", "    print(\"🧠 Analyzing heuristic accuracy using Gemini AI...\")\n", "    \n", "    heuristic_results = []\n", "    \n", "    for i, match in enumerate(matches):\n", "        gt_heuristics = match['gt_item']['heuristics_violated']\n", "        model_heuristics = match['model_item']['heuristics_violated']\n", "        \n", "        print(f\"\\n🔍 Analyzing match {i+1}/{len(matches)}...\")\n", "        \n", "        # Use Gemini to compare heuristic lists\n", "        heuristic_prompt = f\"\"\"\n", "        Compare these two lists of UX heuristics violated for the same issue.\n", "        Analyze semantic similarity and provide detailed comparison.\n", "        \n", "        Ground Truth Heuristics: {gt_heuristics}\n", "        Model Response Heuristics: {model_heuristics}\n", "        \n", "        Context - Issue Location: {match['gt_item']['location']}\n", "        Context - Issue Description: {match['gt_item']['observation'][:200]}...\n", "        \n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"semantic_overlap_score\": <0-100>,\n", "            \"correctly_identified\": [\"list of semantically matching heuristics\"],\n", "            \"missed_heuristics\": [\"list of GT heuristics not captured by model\"],\n", "            \"extra_heuristics\": [\"list of model heuristics not in GT\"],\n", "            \"precision_score\": <0-100>,\n", "            \"recall_score\": <0-100>,\n", "            \"analysis\": \"detailed analysis of the comparison\"\n", "        }}\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = model.generate_content(heuristic_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                heuristic_analysis = json.loads(json_match.group())\n", "                heuristic_analysis['match_index'] = i\n", "                heuristic_analysis['gt_location'] = match['gt_item']['location']\n", "                heuristic_results.append(heuristic_analysis)\n", "                print(f\"✅ Analysis complete - Overlap: {heuristic_analysis['semantic_overlap_score']}%\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in heuristic analysis: {e}\")\n", "    \n", "    return heuristic_results\n", "\n", "# Analyze heuristic accuracy\n", "heuristic_analysis = analyze_heuristic_accuracy_with_gemini(observation_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display heuristic analysis results\n", "if heuristic_analysis:\n", "    print(\"\\n🧠 HEURISTIC ACCURACY ANALYSIS\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Calculate overall metrics\n", "    avg_precision = np.mean([h['precision_score'] for h in heuristic_analysis])\n", "    avg_recall = np.mean([h['recall_score'] for h in heuristic_analysis])\n", "    avg_overlap = np.mean([h['semantic_overlap_score'] for h in heuristic_analysis])\n", "    \n", "    print(f\"Average Precision: {avg_precision:.1f}%\")\n", "    print(f\"Average Recall: {avg_recall:.1f}%\")\n", "    print(f\"Average Semantic Overlap: {avg_overlap:.1f}%\")\n", "    \n", "    # Visualize heuristic accuracy\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # Precision and Recall scores\n", "    locations = [h['gt_location'][:20] + '...' for h in heuristic_analysis]\n", "    precision_scores = [h['precision_score'] for h in heuristic_analysis]\n", "    recall_scores = [h['recall_score'] for h in heuristic_analysis]\n", "    \n", "    x = np.arange(len(locations))\n", "    width = 0.35\n", "    \n", "    ax1.bar(x - width/2, precision_scores, width, label='Precision', color='#3498db')\n", "    ax1.bar(x + width/2, recall_scores, width, label='Recall', color='#e74c3c')\n", "    ax1.set_xlabel('Matched Observations')\n", "    ax1.set_ylabel('Score (%)')\n", "    ax1.set_title('Heuristic Precision & Recall by Observation')\n", "    ax1.set_xticks(x)\n", "    ax1.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Semantic overlap scores\n", "    overlap_scores = [h['semantic_overlap_score'] for h in heuristic_analysis]\n", "    bars = ax2.bar(locations, overlap_scores, color='#2ecc71')\n", "    ax2.set_xlabel('Matched Observations')\n", "    ax2.set_ylabel('Overlap Score (%)')\n", "    ax2.set_title('Semantic Overlap of Heuristics')\n", "    ax2.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Overall metrics comparison\n", "    metrics = ['Precision', 'Recall', 'Semantic Overlap']\n", "    values = [avg_precision, avg_recall, avg_overlap]\n", "    colors = ['#3498db', '#e74c3c', '#2ecc71']\n", "    bars = ax3.bar(metrics, values, color=colors)\n", "    ax3.set_ylabel('Average Score (%)')\n", "    ax3.set_title('Overall Heuristic Analysis Metrics')\n", "    ax3.set_ylim(0, 100)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, values):\n", "        height = bar.get_height()\n", "        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # Distribution of overlap scores\n", "    ax4.hist(overlap_scores, bins=10, color='#9b59b6', alpha=0.7, edgecolor='black')\n", "    ax4.set_xlabel('Semantic Overlap Score (%)')\n", "    ax4.set_ylabel('Frequency')\n", "    ax4.set_title('Distribution of Semantic Overlap Scores')\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"❌ No heuristic analysis results available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Severity Consistency Analysis\n", "\n", "Compare severity ratings between ground truth and model responses using Gemini."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_severity_consistency_with_gemini(matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze severity consistency using Gemini\"\"\"\n", "    print(\"⚖️ Analyzing severity consistency using Gemini AI...\")\n", "    \n", "    severity_results = []\n", "    \n", "    for i, match in enumerate(matches):\n", "        gt_severity = match['gt_item']['severity']\n", "        model_severity = match['model_item']['severity']\n", "        \n", "        print(f\"\\n🔍 Analyzing severity for match {i+1}/{len(matches)}...\")\n", "        \n", "        # Use Gemini to analyze severity appropriateness\n", "        severity_prompt = f\"\"\"\n", "        Analyze the severity rating consistency for this UX issue.\n", "        \n", "        Issue Location: {match['gt_item']['location']}\n", "        Issue Description: {match['gt_item']['observation']}\n", "        \n", "        Ground Truth Severity: {gt_severity}\n", "        Model Response Severity: {model_severity}\n", "        \n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"severity_match\": <true/false>,\n", "            \"severity_appropriateness_score\": <0-100>,\n", "            \"gt_severity_justified\": <true/false>,\n", "            \"model_severity_justified\": <true/false>,\n", "            \"severity_gap_analysis\": \"explanation of any severity differences\",\n", "            \"recommended_severity\": \"High/Medium/Low\",\n", "            \"analysis\": \"detailed analysis of severity consistency\"\n", "        }}\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = model.generate_content(severity_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                severity_analysis = json.loads(json_match.group())\n", "                severity_analysis['match_index'] = i\n", "                severity_analysis['gt_severity'] = gt_severity\n", "                severity_analysis['model_severity'] = model_severity\n", "                severity_analysis['gt_location'] = match['gt_item']['location']\n", "                severity_results.append(severity_analysis)\n", "                print(f\"✅ Severity analysis complete - Match: {severity_analysis['severity_match']}\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in severity analysis: {e}\")\n", "    \n", "    return severity_results\n", "\n", "# Analyze severity consistency\n", "severity_analysis = analyze_severity_consistency_with_gemini(observation_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display severity analysis results\n", "if severity_analysis:\n", "    print(\"\\n⚖️ SEVERITY CONSISTENCY ANALYSIS\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Calculate metrics\n", "    exact_matches = sum(1 for s in severity_analysis if s['severity_match'])\n", "    match_percentage = (exact_matches / len(severity_analysis)) * 100 if severity_analysis else 0\n", "    avg_appropriateness = np.mean([s['severity_appropriateness_score'] for s in severity_analysis])\n", "    \n", "    print(f\"Exact Severity Matches: {exact_matches}/{len(severity_analysis)} ({match_percentage:.1f}%)\")\n", "    print(f\"Average Appropriateness Score: {avg_appropriateness:.1f}%\")\n", "    \n", "    # Create severity comparison visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Severity match pie chart\n", "    match_labels = ['Exact Match', 'Different']\n", "    match_sizes = [exact_matches, len(severity_analysis) - exact_matches]\n", "    match_colors = ['#2ecc71', '#e74c3c']\n", "    ax1.pie(match_sizes, labels=match_labels, colors=match_colors, autopct='%1.1f%%', startangle=90)\n", "    ax1.set_title('Severity Rating Matches', fontsize=14, fontweight='bold')\n", "    \n", "    # Appropriateness scores\n", "    locations = [s['gt_location'][:15] + '...' for s in severity_analysis]\n", "    appropriateness_scores = [s['severity_appropriateness_score'] for s in severity_analysis]\n", "    bars = ax2.bar(locations, appropriateness_scores, color='#3498db')\n", "    ax2.set_xlabel('Matched Observations')\n", "    ax2.set_ylabel('Appropriateness Score (%)')\n", "    ax2.set_title('Severity Appropriateness by Observation')\n", "    ax2.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Display detailed severity comparison\n", "    print(\"\\n📋 Detailed Severity Comparison:\")\n", "    for i, s in enumerate(severity_analysis):\n", "        status = \"✅\" if s['severity_match'] else \"❌\"\n", "        print(f\"{status} {s['gt_location'][:30]}... | GT: {s['gt_severity']} | Model: {s['model_severity']} | Score: {s['severity_appropriateness_score']}%\")\n", "else:\n", "    print(\"❌ No severity analysis results available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: False Positives and False Negatives Analysis\n", "\n", "Identify issues that were hallucinated by the model (false positives) and ground truth issues that were missed (false negatives)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_false_positives_negatives_with_gemini(gt_obs: List[Dict], model_obs: List[Dict], matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze false positives and negatives using Gemini\"\"\"\n", "    print(\"🔍 Analyzing false positives and negatives using Gemini AI...\")\n", "    \n", "    # Get matched indices\n", "    matched_gt_indices = {match['gt_index'] for match in matches}\n", "    matched_model_indices = {match['model_index'] for match in matches}\n", "    \n", "    # False negatives: GT observations not matched\n", "    false_negatives = [obs for i, obs in enumerate(gt_obs) if i not in matched_gt_indices]\n", "    \n", "    # False positives: Model observations not matched\n", "    false_positives = [obs for i, obs in enumerate(model_obs) if i not in matched_model_indices]\n", "    \n", "    print(f\"\\n📊 Found {len(false_negatives)} false negatives (missed GT issues)\")\n", "    print(f\"📊 Found {len(false_positives)} false positives (model hallucinations)\")\n", "    \n", "    # Analyze false negatives with <PERSON>\n", "    fn_analysis = []\n", "    if false_negatives:\n", "        print(\"\\n🔍 Analyzing false negatives...\")\n", "        for i, fn in enumerate(false_negatives):\n", "            fn_prompt = f\"\"\"\n", "            Analyze why this ground truth UX issue might have been missed by the model.\n", "            \n", "            Missed Issue:\n", "            Location: {fn['location']}\n", "            Severity: {fn['severity']}\n", "            Observation: {fn['observation']}\n", "            Heuristics: {fn['heuristics_violated']}\n", "            \n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"issue_complexity\": <1-10>,\n", "                \"issue_visibility\": <1-10>,\n", "                \"likely_reasons_missed\": [\"list of reasons\"],\n", "                \"impact_of_missing\": \"High/Medium/Low\",\n", "                \"analysis\": \"detailed analysis of why this was missed\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(fn_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fn_result = json.loads(json_match.group())\n", "                    fn_result['gt_item'] = fn\n", "                    fn_analysis.append(fn_result)\n", "                    print(f\"✅ FN analysis {i+1}/{len(false_negatives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FN {i+1}: {e}\")\n", "    \n", "    # Analyze false positives with <PERSON>\n", "    fp_analysis = []\n", "    if false_positives:\n", "        print(\"\\n🔍 Analyzing false positives...\")\n", "        for i, fp in enumerate(false_positives):\n", "            fp_prompt = f\"\"\"\n", "            Analyze whether this model-generated UX issue is a valid concern or a hallucination.\n", "            \n", "            Model Issue:\n", "            Location: {fp['location']}\n", "            Severity: {fp['severity']}\n", "            Observation: {fp['observation']}\n", "            Heuristics: {fp['heuristics_violated']}\n", "            \n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"is_valid_issue\": <true/false>,\n", "                \"validity_confidence\": <0-100>,\n", "                \"issue_quality\": <1-10>,\n", "                \"potential_value\": \"High/Medium/Low\",\n", "                \"classification\": \"Valid Addition/Minor Issue/Hallucination\",\n", "                \"analysis\": \"detailed analysis of the issue validity\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(fp_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fp_result = json.loads(json_match.group())\n", "                    fp_result['model_item'] = fp\n", "                    fp_analysis.append(fp_result)\n", "                    print(f\"✅ FP analysis {i+1}/{len(false_positives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FP {i+1}: {e}\")\n", "    \n", "    return {\n", "        'false_negatives': false_negatives,\n", "        'false_positives': false_positives,\n", "        'fn_analysis': fn_analysis,\n", "        'fp_analysis': fp_analysis\n", "    }\n", "\n", "# Analyze false positives and negatives\n", "fp_fn_analysis = analyze_false_positives_negatives_with_gemini(gt_observations, model_observations, observation_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display false positives and negatives analysis\n", "print(\"\\n🔍 FALSE POSITIVES & NEGATIVES ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"\\n❌ FALSE NEGATIVES (Missed GT Issues): {len(fp_fn_analysis['false_negatives'])}\")\n", "for i, fn in enumerate(fp_fn_analysis['false_negatives']):\n", "    print(f\"  {i+1}. {fn['location']} | Severity: {fn['severity']}\")\n", "    print(f\"     {fn['observation'][:100]}...\")\n", "\n", "print(f\"\\n➕ FALSE POSITIVES (Model Additions): {len(fp_fn_analysis['false_positives'])}\")\n", "for i, fp in enumerate(fp_fn_analysis['false_positives']):\n", "    print(f\"  {i+1}. {fp['location']} | Severity: {fp['severity']}\")\n", "    print(f\"     {fp['observation'][:100]}...\")\n", "\n", "# Visualize false positives and negatives\n", "if fp_fn_analysis['fn_analysis'] or fp_fn_analysis['fp_analysis']:\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # False negatives impact analysis\n", "    if fp_fn_analysis['fn_analysis']:\n", "        fn_impacts = [fn['impact_of_missing'] for fn in fp_fn_analysis['fn_analysis']]\n", "        fn_impact_counts = {impact: fn_impacts.count(impact) for impact in ['High', 'Medium', 'Low']}\n", "        ax1.bar(fn_impact_counts.keys(), fn_impact_counts.values(), color=['#e74c3c', '#f39c12', '#2ecc71'])\n", "        ax1.set_title('Impact of Missing Issues (False Negatives)')\n", "        ax1.set_ylabel('Count')\n", "    \n", "    # False positives validity analysis\n", "    if fp_fn_analysis['fp_analysis']:\n", "        fp_classifications = [fp['classification'] for fp in fp_fn_analysis['fp_analysis']]\n", "        fp_class_counts = {}\n", "        for classification in fp_classifications:\n", "            fp_class_counts[classification] = fp_class_counts.get(classification, 0) + 1\n", "        ax2.bar(fp_class_counts.keys(), fp_class_counts.values(), color=['#3498db', '#9b59b6', '#e67e22'])\n", "        ax2.set_title('Classification of Model Additions (False Positives)')\n", "        ax2.set_ylabel('Count')\n", "        ax2.tick_params(axis='x', rotation=45)\n", "    \n", "    # Overall false positive/negative distribution\n", "    categories = ['True Positives\\n(Matches)', 'False Negatives\\n(Missed)', 'False Positives\\n(Added)']\n", "    values = [len(observation_matches), len(fp_fn_analysis['false_negatives']), len(fp_fn_analysis['false_positives'])]\n", "    colors = ['#2ecc71', '#e74c3c', '#f39c12']\n", "    bars = ax3.bar(categories, values, color=colors)\n", "    ax3.set_title('Overall Issue Detection Performance')\n", "    ax3.set_ylabel('Count')\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, values):\n", "        height = bar.get_height()\n", "        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,\n", "                 str(value), ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # Performance metrics pie chart\n", "    total_gt = len(gt_observations)\n", "    total_model = len(model_observations)\n", "    precision = len(observation_matches) / total_model if total_model > 0 else 0\n", "    recall = len(observation_matches) / total_gt if total_gt > 0 else 0\n", "    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "    \n", "    metrics = ['Precision', 'Recall', 'F1-Score']\n", "    metric_values = [precision * 100, recall * 100, f1_score * 100]\n", "    bars = ax4.bar(metrics, metric_values, color=['#3498db', '#e74c3c', '#2ecc71'])\n", "    ax4.set_title('Overall Detection Metrics')\n", "    ax4.set_ylabel('Score (%)')\n", "    ax4.set_ylim(0, 100)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, metric_values):\n", "        height = bar.get_height()\n", "        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(f\"\\n📊 DETECTION PERFORMANCE METRICS\")\n", "print(\"=\" * 40)\n", "total_gt = len(gt_observations)\n", "total_model = len(model_observations)\n", "matches_count = len(observation_matches)\n", "precision = matches_count / total_model if total_model > 0 else 0\n", "recall = matches_count / total_gt if total_gt > 0 else 0\n", "f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "\n", "print(f\"Precision: {precision:.3f} ({precision*100:.1f}%)\")\n", "print(f\"Recall: {recall:.3f} ({recall*100:.1f}%)\")\n", "print(f\"F1-Score: {f1_score:.3f} ({f1_score*100:.1f}%)\")\n", "print(f\"Total GT Issues: {total_gt}\")\n", "print(f\"Total Model Issues: {total_model}\")\n", "print(f\"Successfully Matched: {matches_count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Overall Evaluation Summary Using Gemini\n", "\n", "Generate a comprehensive evaluation summary using all the analysis results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_overall_evaluation_summary(coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches) -> str:\n", "    \"\"\"Generate comprehensive evaluation summary using Gemini\"\"\"\n", "    print(\"📝 Generating overall evaluation summary using Gemini AI...\")\n", "    \n", "    # Prepare summary data\n", "    total_gt = len(gt_observations)\n", "    total_model = len(model_observations)\n", "    matches_count = len(observation_matches)\n", "    \n", "    # Calculate metrics\n", "    coverage_pct = coverage_results['coverage_percentage']\n", "    avg_heuristic_precision = np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    avg_heuristic_recall = np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    severity_match_pct = (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0\n", "    \n", "    summary_prompt = f\"\"\"\n", "    Generate a comprehensive evaluation summary for a UX audit model performance.\n", "    \n", "    EVALUATION RESULTS:\n", "    \n", "    1. OBSERVATION COVERAGE:\n", "    - Total Ground Truth Issues: {total_gt}\n", "    - Total Model Issues: {total_model}\n", "    - Successfully Matched: {matches_count}\n", "    - Coverage Percentage: {coverage_pct:.1f}%\n", "    \n", "    2. HEURISTIC ACCURACY:\n", "    - Average Precision: {avg_heuristic_precision:.1f}%\n", "    - Average Recall: {avg_heuristic_recall:.1f}%\n", "    \n", "    3. SEVERITY CONSISTENCY:\n", "    - Exact Severity Matches: {severity_match_pct:.1f}%\n", "    \n", "    4. FALSE POSITIVES/NEGATIVES:\n", "    - False Negatives (Missed): {len(fp_fn_analysis['false_negatives'])}\n", "    - False Positives (Added): {len(fp_fn_analysis['false_positives'])}\n", "    \n", "    Provide a comprehensive evaluation summary including:\n", "    1. Overall model performance assessment\n", "    2. Key strengths identified\n", "    3. Major weaknesses and areas for improvement\n", "    4. Specific recommendations for model enhancement\n", "    5. Comparative analysis against expected performance\n", "    6. Final grade/rating (A-F scale)\n", "    \n", "    Format as a detailed professional evaluation report.\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(summary_prompt)\n", "        return response.text\n", "    except Exception as e:\n", "        return f\"Error generating summary: {e}\"\n", "\n", "# Generate overall evaluation summary\n", "evaluation_summary = generate_overall_evaluation_summary(\n", "    coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches\n", ")\n", "\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"📋 COMPREHENSIVE EVALUATION SUMMARY\")\n", "print(\"=\" * 80)\n", "print(evaluation_summary)\n", "print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Create Detailed DataFrame and CSV Export\n", "\n", "Generate comprehensive DataFrames with all evaluation metrics for easy analysis and CSV export."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_detailed_evaluation_dataframes(gt_obs, model_obs, matches, heuristic_analysis, severity_analysis, fp_fn_analysis):\n", "    \"\"\"Create comprehensive DataFrames for all evaluation results\"\"\"\n", "    print(\"📊 Creating detailed evaluation DataFrames...\")\n", "    \n", "    # 1. MAIN EVALUATION DATAFRAME (Matched Observations)\n", "    main_data = []\n", "    \n", "    for i, match in enumerate(matches):\n", "        gt_item = match['gt_item']\n", "        model_item = match['model_item']\n", "        \n", "        # Get corresponding heuristic analysis\n", "        heuristic_data = next((h for h in heuristic_analysis if h.get('match_index') == i), {})\n", "        \n", "        # Get corresponding severity analysis\n", "        severity_data = next((s for s in severity_analysis if s.get('match_index') == i), {})\n", "        \n", "        # Calculate record-wise accuracy\n", "        location_score = match['location_similarity']['similarity_score']\n", "        observation_score = match['observation_similarity']['similarity_score']\n", "        heuristic_precision = heuristic_data.get('precision_score', 0)\n", "        heuristic_recall = heuristic_data.get('recall_score', 0)\n", "        severity_score = severity_data.get('severity_appropriateness_score', 0)\n", "        \n", "        # Overall record accuracy (weighted average)\n", "        record_accuracy = (\n", "            location_score * 0.15 + \n", "            observation_score * 0.25 + \n", "            heuristic_precision * 0.25 + \n", "            heuristic_recall * 0.25 + \n", "            severity_score * 0.10\n", "        )\n", "        \n", "        row_data = {\n", "            # Basic Matching Info\n", "            'GT_ID': gt_item['id'],\n", "            'Model_Response_ID': model_item['id'],\n", "            'Match_Type': 'Matched',\n", "            \n", "            # Location Analysis\n", "            'GT_Location': gt_item['location'],\n", "            'Model_Location': model_item['location'],\n", "            'Location_Similarity_Score': location_score,\n", "            'Location_Match_Description': match.get('reasoning', 'Location semantically similar'),\n", "            \n", "            # Observation Analysis\n", "            'GT_Observation': gt_item['observation'][:200] + '...' if len(gt_item['observation']) > 200 else gt_item['observation'],\n", "            'Model_Observation': model_item['observation'][:200] + '...' if len(model_item['observation']) > 200 else model_item['observation'],\n", "            'Observation_Similarity_Score': observation_score,\n", "            'Overall_Matching_Score': match['combined_score'],\n", "            \n", "            # Heuristic Analysis\n", "            'GT_Heuristics': '; '.join(gt_item['heuristics_violated']),\n", "            'Model_Heuristics': '; '.join(model_item['heuristics_violated']),\n", "            'Heuristic_Precision_Score': heuristic_precision,\n", "            'Heuristic_Recall_Score': heuristic_recall,\n", "            'Heuristic_Semantic_Overlap': heuristic_data.get('semantic_overlap_score', 0),\n", "            'Matched_Heuristics': '; '.join(heuristic_data.get('correctly_identified', [])),\n", "            'Unmatched_GT_Heuristics': '; '.join(heuristic_data.get('missed_heuristics', [])),\n", "            'Extra_Model_Heuristics': '; '.join(heuristic_data.get('extra_heuristics', [])),\n", "            'Heuristic_Analysis_Description': heuristic_data.get('analysis', 'No detailed analysis available'),\n", "            \n", "            # Severity Analysis\n", "            'GT_Severity': gt_item['severity'],\n", "            'Model_Severity': model_item['severity'],\n", "            'Severity_Exact_Match': severity_data.get('severity_match', False),\n", "            'Severity_Appropriateness_Score': severity_score,\n", "            'Severity_Analysis_Description': severity_data.get('analysis', 'No detailed analysis available'),\n", "            'Recommended_Severity': severity_data.get('recommended_severity', 'N/A'),\n", "            \n", "            # Overall Record Performance\n", "            'Record_Accuracy_Score': record_accuracy,\n", "            'Record_Performance_Grade': (\n", "                'Excellent' if record_accuracy >= 80 else\n", "                'Good' if record_accuracy >= 65 else\n", "                'Fair' if record_accuracy >= 50 else\n", "                'Poor'\n", "            )\n", "        }\n", "        \n", "        main_data.append(row_data)\n", "    \n", "    main_df = pd.DataFrame(main_data)\n", "    \n", "    # 2. FALSE NEGATIVES DATAFRAME (Missed GT Issues)\n", "    fn_data = []\n", "    for i, fn_item in enumerate(fp_fn_analysis['false_negatives']):\n", "        fn_analysis_data = next((fn for fn in fp_fn_analysis['fn_analysis'] if fn.get('gt_item', {}).get('id') == fn_item['id']), {})\n", "        \n", "        fn_row = {\n", "            'GT_ID': fn_item['id'],\n", "            'Model_Response_ID': 'N/A (Missed)',\n", "            'Match_Type': 'False Negative',\n", "            'GT_Location': fn_item['location'],\n", "            'GT_Severity': fn_item['severity'],\n", "            'GT_Observation': fn_item['observation'][:300] + '...' if len(fn_item['observation']) > 300 else fn_item['observation'],\n", "            'GT_Heuristics': '; '.join(fn_item['heuristics_violated']),\n", "            'Issue_Complexity': fn_analysis_data.get('issue_complexity', 'N/A'),\n", "            'Issue_Visibility': fn_analysis_data.get('issue_visibility', 'N/A'),\n", "            'Impact_of_Missing': fn_analysis_data.get('impact_of_missing', 'N/A'),\n", "            'Likely_Reasons_Missed': '; '.join(fn_analysis_data.get('likely_reasons_missed', [])),\n", "            'Analysis_Description': fn_analysis_data.get('analysis', 'No detailed analysis available')\n", "        }\n", "        fn_data.append(fn_row)\n", "    \n", "    fn_df = pd.DataFrame(fn_data)\n", "    \n", "    # 3. FALSE POSITIVES DATAFRAME (Model Hallucinations)\n", "    fp_data = []\n", "    for i, fp_item in enumerate(fp_fn_analysis['false_positives']):\n", "        fp_analysis_data = next((fp for fp in fp_fn_analysis['fp_analysis'] if fp.get('model_item', {}).get('id') == fp_item['id']), {})\n", "        \n", "        fp_row = {\n", "            'GT_ID': 'N/A (Added by Model)',\n", "            'Model_Response_ID': fp_item['id'],\n", "            'Match_Type': 'False Positive',\n", "            'Model_Location': fp_item['location'],\n", "            'Model_Severity': fp_item['severity'],\n", "            'Model_Observation': fp_item['observation'][:300] + '...' if len(fp_item['observation']) > 300 else fp_item['observation'],\n", "            'Model_Heuristics': '; '.join(fp_item['heuristics_violated']),\n", "            'Is_Valid_Issue': fp_analysis_data.get('is_valid_issue', False),\n", "            'Validity_Confidence': fp_analysis_data.get('validity_confidence', 0),\n", "            'Issue_Quality_Score': fp_analysis_data.get('issue_quality', 0),\n", "            'Potential_Value': fp_analysis_data.get('potential_value', 'N/A'),\n", "            'Classification': fp_analysis_data.get('classification', 'N/A'),\n", "            'Analysis_Description': fp_analysis_data.get('analysis', 'No detailed analysis available')\n", "        }\n", "        fp_data.append(fp_row)\n", "    \n", "    fp_df = pd.DataFrame(fp_data)\n", "    \n", "    return main_df, fn_df, fp_df\n", "\n", "# Create detailed DataFrames\n", "main_evaluation_df, false_negatives_df, false_positives_df = create_detailed_evaluation_dataframes(\n", "    gt_observations, model_observations, observation_matches, \n", "    heuristic_analysis, severity_analysis, fp_fn_analysis\n", ")\n", "\n", "print(\"✅ DataFrames created successfully!\")\n", "print(f\"📊 Main Evaluation Records: {len(main_evaluation_df)}\")\n", "print(f\"❌ False Negatives: {len(false_negatives_df)}\")\n", "print(f\"➕ False Positives: {len(false_positives_df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display sample of main evaluation DataFrame\n", "print(\"\\n📋 MAIN EVALUATION DATAFRAME SAMPLE\")\n", "print(\"=\" * 50)\n", "if not main_evaluation_df.empty:\n", "    # Display key columns for overview\n", "    display_cols = ['GT_ID', 'Model_Response_ID', 'Overall_Matching_Score', \n", "                   'Heuristic_Precision_Score', 'Heuristic_Recall_Score', \n", "                   'Severity_Exact_Match', 'Record_Accuracy_Score', 'Record_Performance_Grade']\n", "    print(main_evaluation_df[display_cols].head(10))\n", "    \n", "    print(\"\\n📊 MAIN EVALUATION STATISTICS\")\n", "    print(f\"Average Overall Matching Score: {main_evaluation_df['Overall_Matching_Score'].mean():.2f}\")\n", "    print(f\"Average Record Accuracy: {main_evaluation_df['Record_Accuracy_Score'].mean():.2f}\")\n", "    print(f\"Average Heuristic Precision: {main_evaluation_df['Heuristic_Precision_Score'].mean():.2f}\")\n", "    print(f\"Average Heuristic Recall: {main_evaluation_df['Heuristic_Recall_Score'].mean():.2f}\")\n", "    print(f\"Severity Exact Matches: {main_evaluation_df['Severity_Exact_Match'].sum()}/{len(main_evaluation_df)}\")\n", "    \n", "    # Performance grade distribution\n", "    grade_dist = main_evaluation_df['Record_Performance_Grade'].value_counts()\n", "    print(\"\\n🎯 Performance Grade Distribution:\")\n", "    for grade, count in grade_dist.items():\n", "        print(f\"  {grade}: {count} records ({count/len(main_evaluation_df)*100:.1f}%)\")\n", "else:\n", "    print(\"No matched observations found.\")\n", "\n", "# Display False Negatives summary\n", "print(\"\\n❌ FALSE NEGATIVES SUMMARY\")\n", "print(\"=\" * 30)\n", "if not false_negatives_df.empty:\n", "    print(false_negatives_df[['GT_ID', 'GT_Location', 'GT_Severity', 'Impact_of_Missing']].head())\n", "    \n", "    # Impact distribution\n", "    if 'Impact_of_Missing' in false_negatives_df.columns:\n", "        impact_dist = false_negatives_df['Impact_of_Missing'].value_counts()\n", "        print(\"\\n📊 Impact Distribution of Missed Issues:\")\n", "        for impact, count in impact_dist.items():\n", "            print(f\"  {impact}: {count} issues\")\n", "else:\n", "    print(\"No false negatives found.\")\n", "\n", "# Display False Positives summary\n", "print(\"\\n➕ FALSE POSITIVES SUMMARY\")\n", "print(\"=\" * 30)\n", "if not false_positives_df.empty:\n", "    print(false_positives_df[['Model_Response_ID', 'Model_Location', 'Model_Severity', 'Classification']].head())\n", "    \n", "    # Classification distribution\n", "    if 'Classification' in false_positives_df.columns:\n", "        class_dist = false_positives_df['Classification'].value_counts()\n", "        print(\"\\n📊 Classification Distribution of Model Additions:\")\n", "        for classification, count in class_dist.items():\n", "            print(f\"  {classification}: {count} issues\")\n", "else:\n", "    print(\"No false positives found.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate Overall Model Coverage Accuracy\n", "def calculate_overall_coverage_accuracy(main_df, fn_df, fp_df, total_gt, total_model):\n", "    \"\"\"Calculate comprehensive coverage accuracy metrics\"\"\"\n", "    \n", "    # Basic metrics\n", "    true_positives = len(main_df)\n", "    false_negatives = len(fn_df)\n", "    false_positives = len(fp_df)\n", "    \n", "    # Coverage metrics\n", "    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0\n", "    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0\n", "    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "    \n", "    # Quality-weighted metrics\n", "    if not main_df.empty:\n", "        avg_matching_quality = main_df['Overall_Matching_Score'].mean()\n", "        avg_record_accuracy = main_df['Record_Accuracy_Score'].mean()\n", "        avg_heuristic_precision = main_df['Heuristic_Precision_Score'].mean()\n", "        avg_heuristic_recall = main_df['Heuristic_Recall_Score'].mean()\n", "        severity_accuracy = main_df['Severity_Exact_Match'].mean() * 100\n", "    else:\n", "        avg_matching_quality = 0\n", "        avg_record_accuracy = 0\n", "        avg_heuristic_precision = 0\n", "        avg_heuristic_recall = 0\n", "        severity_accuracy = 0\n", "    \n", "    # Overall model performance score (weighted combination)\n", "    overall_score = (\n", "        recall * 0.25 +  # Coverage of GT issues\n", "        precision * 0.20 +  # Accuracy of model predictions\n", "        (avg_matching_quality / 100) * 0.20 +  # Quality of matches\n", "        (avg_record_accuracy / 100) * 0.20 +  # Record-level accuracy\n", "        (severity_accuracy / 100) * 0.15  # Severity consistency\n", "    ) * 100\n", "    \n", "    return {\n", "        'total_gt_issues': total_gt,\n", "        'total_model_issues': total_model,\n", "        'true_positives': true_positives,\n", "        'false_negatives': false_negatives,\n", "        'false_positives': false_positives,\n", "        'precision': precision,\n", "        'recall': recall,\n", "        'f1_score': f1_score,\n", "        'coverage_percentage': recall * 100,\n", "        'avg_matching_quality': avg_matching_quality,\n", "        'avg_record_accuracy': avg_record_accuracy,\n", "        'avg_heuristic_precision': avg_heuristic_precision,\n", "        'avg_heuristic_recall': avg_heuristic_recall,\n", "        'severity_accuracy': severity_accuracy,\n", "        'overall_model_score': overall_score,\n", "        'performance_grade': (\n", "            'A+' if overall_score >= 90 else\n", "            'A' if overall_score >= 85 else\n", "            'A-' if overall_score >= 80 else\n", "            'B+' if overall_score >= 75 else\n", "            'B' if overall_score >= 70 else\n", "            'B-' if overall_score >= 65 else\n", "            'C+' if overall_score >= 60 else\n", "            'C' if overall_score >= 55 else\n", "            'C-' if overall_score >= 50 else\n", "            'D' if overall_score >= 40 else\n", "            'F'\n", "        )\n", "    }\n", "\n", "# Calculate overall coverage accuracy\n", "overall_metrics = calculate_overall_coverage_accuracy(\n", "    main_evaluation_df, false_negatives_df, false_positives_df, \n", "    len(gt_observations), len(model_observations)\n", ")\n", "\n", "print(\"\\n🎯 OVERALL MODEL COVERAGE ACCURACY\")\n", "print(\"=\" * 50)\n", "print(f\"📊 Total GT Issues: {overall_metrics['total_gt_issues']}\")\n", "print(f\"📊 Total Model Issues: {overall_metrics['total_model_issues']}\")\n", "print(f\"✅ True Positives (Matched): {overall_metrics['true_positives']}\")\n", "print(f\"❌ False Negatives (Missed): {overall_metrics['false_negatives']}\")\n", "print(f\"➕ False Positives (Added): {overall_metrics['false_positives']}\")\n", "print(f\"\\n📈 PERFORMANCE METRICS:\")\n", "print(f\"   • Precision: {overall_metrics['precision']:.3f} ({overall_metrics['precision']*100:.1f}%)\")\n", "print(f\"   • Recall (Coverage): {overall_metrics['recall']:.3f} ({overall_metrics['coverage_percentage']:.1f}%)\")\n", "print(f\"   • F1-Score: {overall_metrics['f1_score']:.3f} ({overall_metrics['f1_score']*100:.1f}%)\")\n", "print(f\"   • Average Matching Quality: {overall_metrics['avg_matching_quality']:.1f}%\")\n", "print(f\"   • Average Record Accuracy: {overall_metrics['avg_record_accuracy']:.1f}%\")\n", "print(f\"   • Heuristic Precision: {overall_metrics['avg_heuristic_precision']:.1f}%\")\n", "print(f\"   • Heuristic Recall: {overall_metrics['avg_heuristic_recall']:.1f}%\")\n", "print(f\"   • Severity Accuracy: {overall_metrics['severity_accuracy']:.1f}%\")\n", "print(f\"\\n🏆 OVERALL MODEL SCORE: {overall_metrics['overall_model_score']:.1f}% (Grade: {overall_metrics['performance_grade']})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive summary DataFrame for CSV export\n", "def create_summary_dataframe(overall_metrics):\n", "    \"\"\"Create a summary DataFrame with overall metrics\"\"\"\n", "    summary_data = {\n", "        'Metric': [\n", "            'Total Ground Truth Issues',\n", "            'Total Model Response Issues',\n", "            'True Positives (Matched)',\n", "            'False Negatives (Missed GT)',\n", "            'False Positives (Model Added)',\n", "            'Precision',\n", "            'Recall (Coverage)',\n", "            'F1-Score',\n", "            'Average Matching Quality Score',\n", "            'Average Record Accuracy Score',\n", "            'Average Heuristic Precision',\n", "            'Average Heuristic Recall',\n", "            'Severity Accuracy',\n", "            'Overall Model Performance Score',\n", "            'Performance Grade'\n", "        ],\n", "        'Value': [\n", "            overall_metrics['total_gt_issues'],\n", "            overall_metrics['total_model_issues'],\n", "            overall_metrics['true_positives'],\n", "            overall_metrics['false_negatives'],\n", "            overall_metrics['false_positives'],\n", "            f\"{overall_metrics['precision']:.3f} ({overall_metrics['precision']*100:.1f}%)\",\n", "            f\"{overall_metrics['recall']:.3f} ({overall_metrics['coverage_percentage']:.1f}%)\",\n", "            f\"{overall_metrics['f1_score']:.3f} ({overall_metrics['f1_score']*100:.1f}%)\",\n", "            f\"{overall_metrics['avg_matching_quality']:.1f}%\",\n", "            f\"{overall_metrics['avg_record_accuracy']:.1f}%\",\n", "            f\"{overall_metrics['avg_heuristic_precision']:.1f}%\",\n", "            f\"{overall_metrics['avg_heuristic_recall']:.1f}%\",\n", "            f\"{overall_metrics['severity_accuracy']:.1f}%\",\n", "            f\"{overall_metrics['overall_model_score']:.1f}%\",\n", "            overall_metrics['performance_grade']\n", "        ],\n", "        'Description': [\n", "            'Total number of issues in ground truth data',\n", "            'Total number of issues identified by the model',\n", "            'Number of GT issues correctly identified by model',\n", "            'Number of GT issues missed by the model',\n", "            'Number of additional issues identified by model',\n", "            'Accuracy of model predictions (TP / (TP + FP))',\n", "            'Coverage of GT issues by model (TP / (TP + FN))',\n", "            'Harmonic mean of precision and recall',\n", "            'Average semantic similarity score of matched observations',\n", "            'Average overall accuracy score per matched record',\n", "            'Average precision of heuristic identification',\n", "            'Average recall of heuristic identification',\n", "            'Percentage of exact severity matches',\n", "            'Weighted overall performance score (0-100%)',\n", "            'Letter grade based on overall performance'\n", "        ]\n", "    }\n", "    \n", "    return pd.DataFrame(summary_data)\n", "\n", "# Create summary DataFrame\n", "summary_df = create_summary_dataframe(overall_metrics)\n", "\n", "print(\"\\n📋 EVALUATION SUMMARY TABLE\")\n", "print(\"=\" * 80)\n", "print(summary_df.to_string(index=False, max_colwidth=50))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Export to CSV Files\n", "\n", "Save all DataFrames to CSV files for detailed analysis and reporting."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export all DataFrames to CSV files\n", "timestamp = pd.Timestamp.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# File names\n", "main_csv = f'ux_audit_main_evaluation_{timestamp}.csv'\n", "fn_csv = f'ux_audit_false_negatives_{timestamp}.csv'\n", "fp_csv = f'ux_audit_false_positives_{timestamp}.csv'\n", "summary_csv = f'ux_audit_summary_{timestamp}.csv'\n", "combined_csv = f'ux_audit_complete_evaluation_{timestamp}.csv'\n", "\n", "# Export individual DataFrames\n", "try:\n", "    # Main evaluation data\n", "    if not main_evaluation_df.empty:\n", "        main_evaluation_df.to_csv(main_csv, index=False, encoding='utf-8')\n", "        print(f\"✅ Main evaluation data exported to: {main_csv}\")\n", "    \n", "    # False negatives\n", "    if not false_negatives_df.empty:\n", "        false_negatives_df.to_csv(fn_csv, index=False, encoding='utf-8')\n", "        print(f\"✅ False negatives data exported to: {fn_csv}\")\n", "    \n", "    # False positives\n", "    if not false_positives_df.empty:\n", "        false_positives_df.to_csv(fp_csv, index=False, encoding='utf-8')\n", "        print(f\"✅ False positives data exported to: {fp_csv}\")\n", "    \n", "    # Summary metrics\n", "    summary_df.to_csv(summary_csv, index=False, encoding='utf-8')\n", "    print(f\"✅ Summary metrics exported to: {summary_csv}\")\n", "    \n", "    # Create combined DataFrame with all data\n", "    combined_data = []\n", "    \n", "    # Add summary section\n", "    combined_data.append(['=== EVALUATION SUMMARY ==='])\n", "    for _, row in summary_df.iterrows():\n", "        combined_data.append([row['Metric'], row['Value'], row['Description']])\n", "    \n", "    combined_data.append([''])  # Empty row\n", "    combined_data.append(['=== MATCHED OBSERVATIONS (TRUE POSITIVES) ==='])\n", "    \n", "    # Add main evaluation data\n", "    if not main_evaluation_df.empty:\n", "        # Add headers\n", "        combined_data.append(list(main_evaluation_df.columns))\n", "        # Add data\n", "        for _, row in main_evaluation_df.iterrows():\n", "            combined_data.append(list(row))\n", "    \n", "    combined_data.append([''])  # Empty row\n", "    combined_data.append(['=== FALSE NEGATIVES (MISSED GT ISSUES) ==='])\n", "    \n", "    # Add false negatives data\n", "    if not false_negatives_df.empty:\n", "        combined_data.append(list(false_negatives_df.columns))\n", "        for _, row in false_negatives_df.iterrows():\n", "            combined_data.append(list(row))\n", "    \n", "    combined_data.append([''])  # Empty row\n", "    combined_data.append(['=== FALSE POSITIVES (MODEL ADDITIONS) ==='])\n", "    \n", "    # Add false positives data\n", "    if not false_positives_df.empty:\n", "        combined_data.append(list(false_positives_df.columns))\n", "        for _, row in false_positives_df.iterrows():\n", "            combined_data.append(list(row))\n", "    \n", "    # Save combined CSV\n", "    combined_df = pd.DataFrame(combined_data)\n", "    combined_df.to_csv(combined_csv, index=False, header=False, encoding='utf-8')\n", "    print(f\"✅ Complete evaluation data exported to: {combined_csv}\")\n", "    \n", "    print(f\"\\n📁 All CSV files saved with timestamp: {timestamp}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error exporting CSV files: {e}\")\n", "\n", "# Also save JSON results for programmatic access\n", "json_results = {\n", "    'evaluation_metadata': {\n", "        'timestamp': pd.Timestamp.now().isoformat(),\n", "        'total_gt_observations': len(gt_observations),\n", "        'total_model_observations': len(model_observations)\n", "    },\n", "    'overall_metrics': overall_metrics,\n", "    'evaluation_summary': evaluation_summary,\n", "    'detailed_results': {\n", "        'matched_observations': main_evaluation_df.to_dict('records') if not main_evaluation_df.empty else [],\n", "        'false_negatives': false_negatives_df.to_dict('records') if not false_negatives_df.empty else [],\n", "        'false_positives': false_positives_df.to_dict('records') if not false_positives_df.empty else []\n", "    }\n", "}\n", "\n", "json_filename = f'ux_audit_evaluation_results_{timestamp}.json'\n", "with open(json_filename, 'w', encoding='utf-8') as f:\n", "    json.dump(json_results, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "print(f\"✅ JSON results exported to: {json_filename}\")\n", "\n", "print(\"\\n🎉 UX AUDIT EVALUATION COMPLETE!\")\n", "print(\"=\" * 50)\n", "print(f\"📊 Final Model Performance: {overall_metrics['overall_model_score']:.1f}% (Grade: {overall_metrics['performance_grade']})\")\n", "print(f\"📈 Coverage: {overall_metrics['coverage_percentage']:.1f}%\")\n", "print(f\"🎯 Precision: {overall_metrics['precision']*100:.1f}%\")\n", "print(f\"📋 Total Files Generated: 5 CSV + 1 JSON\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}