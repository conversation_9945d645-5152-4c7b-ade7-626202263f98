{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UX Audit Report Evaluation Using Gemini AI\n", "\n", "This notebook evaluates model-generated UX audit reports against ground truth data using Google's Gemini AI model.\n", "\n", "## Evaluation Parameters:\n", "1. **Observation Coverage** - How many GT observations were matched by the model\n", "2. **Heuristic Match Accuracy** - Precision, recall, and F1-score for heuristics\n", "3. **Severity Consistency** - Comparison of severity ratings\n", "4. **Location Accuracy** - Semantic similarity of issue locations\n", "5. **Observation Similarity** - Semantic comparison of observation texts\n", "6. **False Positives/Hallucinations** - Issues invented by the model\n", "7. **False Negatives/Omissions** - GT issues missed by the model\n", "8. **Overall Evaluation Summary** - Comprehensive analysis by Gemini"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-generativeai pandas numpy mat<PERSON><PERSON><PERSON>b seaborn scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Any\n", "import google.generativeai as genai\n", "from sklearn.metrics import precision_recall_fscore_support\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure Gemini API\n", "# Replace 'YOUR_API_KEY' with your actual Gemini API key\n", "GEMINI_API_KEY = 'YOUR_API_KEY'  # Get from https://makersuite.google.com/app/apikey\n", "genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "# Initialize Gemini model\n", "model = genai.GenerativeModel('gemini-1.5-flash')\n", "\n", "print(\"✅ Gemini AI configured successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data files\n", "def load_json_data(file_path: str) -> Dict:\n", "    \"\"\"Load JSON data from file\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return json.load(f)\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return {}\n", "\n", "# Load ground truth and model response data\n", "ground_truth = load_json_data('ground_truth.json')\n", "model_response = load_json_data('model_response.json')\n", "\n", "# Extract observations\n", "gt_observations = ground_truth.get('audit_report', {}).get('observations', [])\n", "model_observations = model_response.get('audit_report', {}).get('observations', [])\n", "\n", "print(f\"📊 Loaded {len(gt_observations)} ground truth observations\")\n", "print(f\"📊 Loaded {len(model_observations)} model response observations\")\n", "print(\"\\n✅ Data loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Location and Observation Matching Using Gemini\n", "\n", "We use Gemini to semantically match observations between ground truth and model response based on location similarity and observation content overlap."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_gemini_similarity_score(text1: str, text2: str, context: str = \"\") -> Dict:\n", "    \"\"\"Get semantic similarity score and analysis from Gemini\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two UX audit observations and provide:\n", "    1. Similarity score (0-100)\n", "    2. Brief analysis of similarities and differences\n", "    3. Whether they refer to the same UX issue (yes/no)\n", "    \n", "    Context: {context}\n", "    \n", "    Text 1: {text1}\n", "    Text 2: {text2}\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"similarity_score\": <0-100>,\n", "        \"same_issue\": <true/false>,\n", "        \"analysis\": \"<brief analysis>\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error in Gemini API call: {e}\")\n", "        return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": f\"API Error: {e}\"}\n", "\n", "print(\"🔧 Gemini similarity function ready!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_observation_matches_optimized(gt_obs: List[Dict], model_obs: List[Dict]) -> List[Dict]:\n", "    \"\"\"Find optimal 1:1 matches between GT and model observations using Gemini efficiently\"\"\"\n", "    print(\"🔍 Finding optimal observation matches using Gemini AI...\")\n", "    print(f\"📊 Processing {len(gt_obs)} GT observations vs {len(model_obs)} model observations\")\n", "    \n", "    # Step 1: Batch similarity analysis for all pairs\n", "    print(\"\\n🚀 Step 1: <PERSON><PERSON> analyzing all GT-Model pairs...\")\n", "    \n", "    similarity_matrix = []\n", "    \n", "    # Create batch prompt for all comparisons to reduce API calls\n", "    batch_prompt = f\"\"\"\n", "    Compare these UX audit observations and provide similarity scores for each pair.\n", "    For each comparison, consider both location similarity and observation content similarity.\n", "    \n", "    GROUND TRUTH OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": gt[\"location\"], \"observation\": gt[\"observation\"][:200] + \"...\"} for i, gt in enumerate(gt_obs)], indent=2)}\n", "    \n", "    MODEL RESPONSE OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": model[\"location\"], \"observation\": model[\"observation\"][:200] + \"...\"} for i, model in enumerate(model_obs)], indent=2)}\n", "    \n", "    For each GT observation, find the best matching model observation and provide:\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"matches\": [\n", "            {{\n", "                \"gt_id\": <gt_index>,\n", "                \"best_model_id\": <model_index or -1 if no good match>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "        ]\n", "    }}\n", "    \n", "    Only consider matches with combined_score > 40. Set best_model_id to -1 if no good match exists.\n", "    \"\"\"\n", "    \n", "    try:\n", "        print(\"🤖 Sending batch request to Gemini...\")\n", "        response = model.generate_content(batch_prompt)\n", "        \n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            batch_results = json.loads(json_match.group())\n", "            potential_matches = batch_results.get('matches', [])\n", "            print(f\"✅ Batch analysis complete! Found {len(potential_matches)} potential matches\")\n", "        else:\n", "            print(\"❌ Failed to parse batch response, falling back to individual analysis\")\n", "            potential_matches = []\n", "    except Exception as e:\n", "        print(f\"❌ Batch analysis failed: {e}\")\n", "        print(\"🔄 Falling back to optimized individual analysis...\")\n", "        potential_matches = []\n", "    \n", "    # Step 2: If batch failed, use optimized individual analysis\n", "    if not potential_matches:\n", "        potential_matches = []\n", "        for i, gt_item in enumerate(gt_obs):\n", "            print(f\"\\n📍 Processing GT {i+1}/{len(gt_obs)}: {gt_item['location'][:30]}...\")\n", "            \n", "            # Quick location-based filtering first\n", "            candidate_models = []\n", "            for j, model_item in enumerate(model_obs):\n", "                # Simple keyword overlap check for initial filtering\n", "                gt_keywords = set(gt_item['location'].lower().split())\n", "                model_keywords = set(model_item['location'].lower().split())\n", "                keyword_overlap = len(gt_keywords.intersection(model_keywords)) / max(len(gt_keywords), 1)\n", "                \n", "                if keyword_overlap > 0.2:  # At least 20% keyword overlap\n", "                    candidate_models.append((j, model_item, keyword_overlap))\n", "            \n", "            # Sort by keyword overlap and take top 3 candidates\n", "            candidate_models.sort(key=lambda x: x[2], reverse=True)\n", "            top_candidates = candidate_models[:3]\n", "            \n", "            if not top_candidates:\n", "                print(f\"  ❌ No location candidates found\")\n", "                potential_matches.append({\n", "                    'gt_id': i,\n", "                    'best_model_id': -1,\n", "                    'combined_score': 0,\n", "                    'is_valid_match': <PERSON><PERSON><PERSON>,\n", "                    'reasoning': 'No location similarity found'\n", "                })\n", "                continue\n", "            \n", "            # Use Gemini only for top candidates\n", "            best_match = None\n", "            best_score = 0\n", "            \n", "            candidates_text = \"\\n\".join([f\"Model {idx}: {item['location']} - {item['observation'][:100]}...\" \n", "                                        for idx, item, _ in top_candidates])\n", "            \n", "            candidate_prompt = f\"\"\"\n", "            Find the best match for this GT observation among the candidates:\n", "            \n", "            GT Observation:\n", "            Location: {gt_item['location']}\n", "            Description: {gt_item['observation'][:200]}...\n", "            \n", "            Candidates:\n", "            {candidates_text}\n", "            \n", "            Respond in JSON format:\n", "            {{\n", "                \"best_model_id\": <model_index or -1>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(candidate_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    result = json.loads(json_match.group())\n", "                    result['gt_id'] = i\n", "                    potential_matches.append(result)\n", "                    if result['is_valid_match']:\n", "                        print(f\"  ✅ Match found: Model {result['best_model_id']} (Score: {result['combined_score']})\")\n", "                    else:\n", "                        print(f\"  ❌ No valid match (Best score: {result['combined_score']})\")\n", "                else:\n", "                    print(f\"  ❌ Failed to parse response\")\n", "            except Exception as e:\n", "                print(f\"  ❌ Error: {e}\")\n", "    \n", "    # Step 3: Resolve conflicts and create optimal 1:1 matching\n", "    print(\"\\n🎯 Step 3: Resolving conflicts for optimal 1:1 matching...\")\n", "    \n", "    # Filter valid matches and sort by score\n", "    valid_matches = [m for m in potential_matches if m['is_valid_match'] and m['best_model_id'] != -1]\n", "    valid_matches.sort(key=lambda x: x['combined_score'], reverse=True)\n", "    \n", "    # Resolve conflicts using Hungarian-like approach (greedy for simplicity)\n", "    used_model_ids = set()\n", "    used_gt_ids = set()\n", "    final_matches = []\n", "    \n", "    for match in valid_matches:\n", "        gt_id = match['gt_id']\n", "        model_id = match['best_model_id']\n", "        \n", "        if gt_id not in used_gt_ids and model_id not in used_model_ids:\n", "            # Create final match object\n", "            final_match = {\n", "                'gt_index': gt_id,\n", "                'model_index': model_id,\n", "                'gt_item': gt_obs[gt_id],\n", "                'model_item': model_obs[model_id],\n", "                'location_similarity': {'similarity_score': match['location_similarity']},\n", "                'observation_similarity': {'similarity_score': match['observation_similarity']},\n", "                'combined_score': match['combined_score'],\n", "                'reasoning': match['reasoning']\n", "            }\n", "            final_matches.append(final_match)\n", "            used_gt_ids.add(gt_id)\n", "            used_model_ids.add(model_id)\n", "    \n", "    print(f\"\\n🎯 Final Results: {len(final_matches)} optimal 1:1 matches found\")\n", "    print(f\"📊 GT Coverage: {len(final_matches)}/{len(gt_obs)} ({len(final_matches)/len(gt_obs)*100:.1f}%)\")\n", "    print(f\"📊 Model Coverage: {len(final_matches)}/{len(model_obs)} ({len(final_matches)/len(model_obs)*100:.1f}%)\")\n", "    \n", "    return final_matches\n", "\n", "# Find optimal matches between observations\n", "observation_matches = find_observation_matches_optimized(gt_observations, model_observations)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Observation Coverage Analysis\n", "\n", "Calculate how many ground truth observations were successfully matched by the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_observation_coverage(matches: List[Dict], total_gt: int) -> Dict:\n", "    \"\"\"Calculate observation coverage metrics\"\"\"\n", "    coverage_score = len(matches) / total_gt if total_gt > 0 else 0\n", "    \n", "    coverage_analysis = {\n", "        'total_gt_observations': total_gt,\n", "        'matched_observations': len(matches),\n", "        'coverage_percentage': coverage_score * 100,\n", "        'unmatched_observations': total_gt - len(matches)\n", "    }\n", "    \n", "    return coverage_analysis\n", "\n", "# Calculate coverage\n", "coverage_results = calculate_observation_coverage(observation_matches, len(gt_observations))\n", "\n", "print(\"📊 OBSERVATION COVERAGE ANALYSIS\")\n", "print(\"=\" * 40)\n", "print(f\"Total Ground Truth Observations: {coverage_results['total_gt_observations']}\")\n", "print(f\"Successfully Matched: {coverage_results['matched_observations']}\")\n", "print(f\"Coverage Percentage: {coverage_results['coverage_percentage']:.1f}%\")\n", "print(f\"Unmatched Observations: {coverage_results['unmatched_observations']}\")\n", "\n", "# Visualize coverage\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Coverage pie chart\n", "labels = ['Matched', 'Unmatched']\n", "sizes = [coverage_results['matched_observations'], coverage_results['unmatched_observations']]\n", "colors = ['#2ecc71', '#e74c3c']\n", "ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)\n", "ax1.set_title('Observation Coverage', fontsize=14, fontweight='bold')\n", "\n", "# Coverage bar chart\n", "categories = ['Coverage %', 'Missing %']\n", "values = [coverage_results['coverage_percentage'], 100 - coverage_results['coverage_percentage']]\n", "bars = ax2.bar(categories, values, color=colors)\n", "ax2.set_ylabel('Percentage')\n", "ax2.set_title('Coverage vs Missing Observations', fontsize=14, fontweight='bold')\n", "ax2.set_ylim(0, 100)\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, values):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "             f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Heuristic Match Accuracy Analysis\n", "\n", "For each matched observation, analyze how well the model identified the violated heuristics using Gemini."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_heuristic_accuracy_with_gemini(matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze heuristic accuracy using Gemini for semantic comparison\"\"\"\n", "    print(\"🧠 Analyzing heuristic accuracy using Gemini AI...\")\n", "    \n", "    heuristic_results = []\n", "    \n", "    for i, match in enumerate(matches):\n", "        gt_heuristics = match['gt_item']['heuristics_violated']\n", "        model_heuristics = match['model_item']['heuristics_violated']\n", "        \n", "        print(f\"\\n🔍 Analyzing match {i+1}/{len(matches)}...\")\n", "        \n", "        # Use Gemini to compare heuristic lists\n", "        heuristic_prompt = f\"\"\"\n", "        Compare these two lists of UX heuristics violated for the same issue.\n", "        Analyze semantic similarity and provide detailed comparison.\n", "        \n", "        Ground Truth Heuristics: {gt_heuristics}\n", "        Model Response Heuristics: {model_heuristics}\n", "        \n", "        Context - Issue Location: {match['gt_item']['location']}\n", "        Context - Issue Description: {match['gt_item']['observation'][:200]}...\n", "        \n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"semantic_overlap_score\": <0-100>,\n", "            \"correctly_identified\": [\"list of semantically matching heuristics\"],\n", "            \"missed_heuristics\": [\"list of GT heuristics not captured by model\"],\n", "            \"extra_heuristics\": [\"list of model heuristics not in GT\"],\n", "            \"precision_score\": <0-100>,\n", "            \"recall_score\": <0-100>,\n", "            \"analysis\": \"detailed analysis of the comparison\"\n", "        }}\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = model.generate_content(heuristic_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                heuristic_analysis = json.loads(json_match.group())\n", "                heuristic_analysis['match_index'] = i\n", "                heuristic_analysis['gt_location'] = match['gt_item']['location']\n", "                heuristic_results.append(heuristic_analysis)\n", "                print(f\"✅ Analysis complete - Overlap: {heuristic_analysis['semantic_overlap_score']}%\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in heuristic analysis: {e}\")\n", "    \n", "    return heuristic_results\n", "\n", "# Analyze heuristic accuracy\n", "heuristic_analysis = analyze_heuristic_accuracy_with_gemini(observation_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display heuristic analysis results\n", "if heuristic_analysis:\n", "    print(\"\\n🧠 HEURISTIC ACCURACY ANALYSIS\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Calculate overall metrics\n", "    avg_precision = np.mean([h['precision_score'] for h in heuristic_analysis])\n", "    avg_recall = np.mean([h['recall_score'] for h in heuristic_analysis])\n", "    avg_overlap = np.mean([h['semantic_overlap_score'] for h in heuristic_analysis])\n", "    \n", "    print(f\"Average Precision: {avg_precision:.1f}%\")\n", "    print(f\"Average Recall: {avg_recall:.1f}%\")\n", "    print(f\"Average Semantic Overlap: {avg_overlap:.1f}%\")\n", "    \n", "    # Visualize heuristic accuracy\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # Precision and Recall scores\n", "    locations = [h['gt_location'][:20] + '...' for h in heuristic_analysis]\n", "    precision_scores = [h['precision_score'] for h in heuristic_analysis]\n", "    recall_scores = [h['recall_score'] for h in heuristic_analysis]\n", "    \n", "    x = np.arange(len(locations))\n", "    width = 0.35\n", "    \n", "    ax1.bar(x - width/2, precision_scores, width, label='Precision', color='#3498db')\n", "    ax1.bar(x + width/2, recall_scores, width, label='Recall', color='#e74c3c')\n", "    ax1.set_xlabel('Matched Observations')\n", "    ax1.set_ylabel('Score (%)')\n", "    ax1.set_title('Heuristic Precision & Recall by Observation')\n", "    ax1.set_xticks(x)\n", "    ax1.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Semantic overlap scores\n", "    overlap_scores = [h['semantic_overlap_score'] for h in heuristic_analysis]\n", "    bars = ax2.bar(locations, overlap_scores, color='#2ecc71')\n", "    ax2.set_xlabel('Matched Observations')\n", "    ax2.set_ylabel('Overlap Score (%)')\n", "    ax2.set_title('Semantic Overlap of Heuristics')\n", "    ax2.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Overall metrics comparison\n", "    metrics = ['Precision', 'Recall', 'Semantic Overlap']\n", "    values = [avg_precision, avg_recall, avg_overlap]\n", "    colors = ['#3498db', '#e74c3c', '#2ecc71']\n", "    bars = ax3.bar(metrics, values, color=colors)\n", "    ax3.set_ylabel('Average Score (%)')\n", "    ax3.set_title('Overall Heuristic Analysis Metrics')\n", "    ax3.set_ylim(0, 100)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, values):\n", "        height = bar.get_height()\n", "        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # Distribution of overlap scores\n", "    ax4.hist(overlap_scores, bins=10, color='#9b59b6', alpha=0.7, edgecolor='black')\n", "    ax4.set_xlabel('Semantic Overlap Score (%)')\n", "    ax4.set_ylabel('Frequency')\n", "    ax4.set_title('Distribution of Semantic Overlap Scores')\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"❌ No heuristic analysis results available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Severity Consistency Analysis\n", "\n", "Compare severity ratings between ground truth and model responses using Gemini."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_severity_consistency_with_gemini(matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze severity consistency using Gemini\"\"\"\n", "    print(\"⚖️ Analyzing severity consistency using Gemini AI...\")\n", "    \n", "    severity_results = []\n", "    \n", "    for i, match in enumerate(matches):\n", "        gt_severity = match['gt_item']['severity']\n", "        model_severity = match['model_item']['severity']\n", "        \n", "        print(f\"\\n🔍 Analyzing severity for match {i+1}/{len(matches)}...\")\n", "        \n", "        # Use Gemini to analyze severity appropriateness\n", "        severity_prompt = f\"\"\"\n", "        Analyze the severity rating consistency for this UX issue.\n", "        \n", "        Issue Location: {match['gt_item']['location']}\n", "        Issue Description: {match['gt_item']['observation']}\n", "        \n", "        Ground Truth Severity: {gt_severity}\n", "        Model Response Severity: {model_severity}\n", "        \n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"severity_match\": <true/false>,\n", "            \"severity_appropriateness_score\": <0-100>,\n", "            \"gt_severity_justified\": <true/false>,\n", "            \"model_severity_justified\": <true/false>,\n", "            \"severity_gap_analysis\": \"explanation of any severity differences\",\n", "            \"recommended_severity\": \"High/Medium/Low\",\n", "            \"analysis\": \"detailed analysis of severity consistency\"\n", "        }}\n", "        \"\"\"\n", "        \n", "        try:\n", "            response = model.generate_content(severity_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                severity_analysis = json.loads(json_match.group())\n", "                severity_analysis['match_index'] = i\n", "                severity_analysis['gt_severity'] = gt_severity\n", "                severity_analysis['model_severity'] = model_severity\n", "                severity_analysis['gt_location'] = match['gt_item']['location']\n", "                severity_results.append(severity_analysis)\n", "                print(f\"✅ Severity analysis complete - Match: {severity_analysis['severity_match']}\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in severity analysis: {e}\")\n", "    \n", "    return severity_results\n", "\n", "# Analyze severity consistency\n", "severity_analysis = analyze_severity_consistency_with_gemini(observation_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display severity analysis results\n", "if severity_analysis:\n", "    print(\"\\n⚖️ SEVERITY CONSISTENCY ANALYSIS\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Calculate metrics\n", "    exact_matches = sum(1 for s in severity_analysis if s['severity_match'])\n", "    match_percentage = (exact_matches / len(severity_analysis)) * 100 if severity_analysis else 0\n", "    avg_appropriateness = np.mean([s['severity_appropriateness_score'] for s in severity_analysis])\n", "    \n", "    print(f\"Exact Severity Matches: {exact_matches}/{len(severity_analysis)} ({match_percentage:.1f}%)\")\n", "    print(f\"Average Appropriateness Score: {avg_appropriateness:.1f}%\")\n", "    \n", "    # Create severity comparison visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Severity match pie chart\n", "    match_labels = ['Exact Match', 'Different']\n", "    match_sizes = [exact_matches, len(severity_analysis) - exact_matches]\n", "    match_colors = ['#2ecc71', '#e74c3c']\n", "    ax1.pie(match_sizes, labels=match_labels, colors=match_colors, autopct='%1.1f%%', startangle=90)\n", "    ax1.set_title('Severity Rating Matches', fontsize=14, fontweight='bold')\n", "    \n", "    # Appropriateness scores\n", "    locations = [s['gt_location'][:15] + '...' for s in severity_analysis]\n", "    appropriateness_scores = [s['severity_appropriateness_score'] for s in severity_analysis]\n", "    bars = ax2.bar(locations, appropriateness_scores, color='#3498db')\n", "    ax2.set_xlabel('Matched Observations')\n", "    ax2.set_ylabel('Appropriateness Score (%)')\n", "    ax2.set_title('Severity Appropriateness by Observation')\n", "    ax2.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Display detailed severity comparison\n", "    print(\"\\n📋 Detailed Severity Comparison:\")\n", "    for i, s in enumerate(severity_analysis):\n", "        status = \"✅\" if s['severity_match'] else \"❌\"\n", "        print(f\"{status} {s['gt_location'][:30]}... | GT: {s['gt_severity']} | Model: {s['model_severity']} | Score: {s['severity_appropriateness_score']}%\")\n", "else:\n", "    print(\"❌ No severity analysis results available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: False Positives and False Negatives Analysis\n", "\n", "Identify issues that were hallucinated by the model (false positives) and ground truth issues that were missed (false negatives)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_false_positives_negatives_with_gemini(gt_obs: List[Dict], model_obs: List[Dict], matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze false positives and negatives using Gemini\"\"\"\n", "    print(\"🔍 Analyzing false positives and negatives using Gemini AI...\")\n", "    \n", "    # Get matched indices\n", "    matched_gt_indices = {match['gt_index'] for match in matches}\n", "    matched_model_indices = {match['model_index'] for match in matches}\n", "    \n", "    # False negatives: GT observations not matched\n", "    false_negatives = [obs for i, obs in enumerate(gt_obs) if i not in matched_gt_indices]\n", "    \n", "    # False positives: Model observations not matched\n", "    false_positives = [obs for i, obs in enumerate(model_obs) if i not in matched_model_indices]\n", "    \n", "    print(f\"\\n📊 Found {len(false_negatives)} false negatives (missed GT issues)\")\n", "    print(f\"📊 Found {len(false_positives)} false positives (model hallucinations)\")\n", "    \n", "    # Analyze false negatives with <PERSON>\n", "    fn_analysis = []\n", "    if false_negatives:\n", "        print(\"\\n🔍 Analyzing false negatives...\")\n", "        for i, fn in enumerate(false_negatives):\n", "            fn_prompt = f\"\"\"\n", "            Analyze why this ground truth UX issue might have been missed by the model.\n", "            \n", "            Missed Issue:\n", "            Location: {fn['location']}\n", "            Severity: {fn['severity']}\n", "            Observation: {fn['observation']}\n", "            Heuristics: {fn['heuristics_violated']}\n", "            \n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"issue_complexity\": <1-10>,\n", "                \"issue_visibility\": <1-10>,\n", "                \"likely_reasons_missed\": [\"list of reasons\"],\n", "                \"impact_of_missing\": \"High/Medium/Low\",\n", "                \"analysis\": \"detailed analysis of why this was missed\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(fn_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fn_result = json.loads(json_match.group())\n", "                    fn_result['gt_item'] = fn\n", "                    fn_analysis.append(fn_result)\n", "                    print(f\"✅ FN analysis {i+1}/{len(false_negatives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FN {i+1}: {e}\")\n", "    \n", "    # Analyze false positives with <PERSON>\n", "    fp_analysis = []\n", "    if false_positives:\n", "        print(\"\\n🔍 Analyzing false positives...\")\n", "        for i, fp in enumerate(false_positives):\n", "            fp_prompt = f\"\"\"\n", "            Analyze whether this model-generated UX issue is a valid concern or a hallucination.\n", "            \n", "            Model Issue:\n", "            Location: {fp['location']}\n", "            Severity: {fp['severity']}\n", "            Observation: {fp['observation']}\n", "            Heuristics: {fp['heuristics_violated']}\n", "            \n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"is_valid_issue\": <true/false>,\n", "                \"validity_confidence\": <0-100>,\n", "                \"issue_quality\": <1-10>,\n", "                \"potential_value\": \"High/Medium/Low\",\n", "                \"classification\": \"Valid Addition/Minor Issue/Hallucination\",\n", "                \"analysis\": \"detailed analysis of the issue validity\"\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(fp_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fp_result = json.loads(json_match.group())\n", "                    fp_result['model_item'] = fp\n", "                    fp_analysis.append(fp_result)\n", "                    print(f\"✅ FP analysis {i+1}/{len(false_positives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FP {i+1}: {e}\")\n", "    \n", "    return {\n", "        'false_negatives': false_negatives,\n", "        'false_positives': false_positives,\n", "        'fn_analysis': fn_analysis,\n", "        'fp_analysis': fp_analysis\n", "    }\n", "\n", "# Analyze false positives and negatives\n", "fp_fn_analysis = analyze_false_positives_negatives_with_gemini(gt_observations, model_observations, observation_matches)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display false positives and negatives analysis\n", "print(\"\\n🔍 FALSE POSITIVES & NEGATIVES ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"\\n❌ FALSE NEGATIVES (Missed GT Issues): {len(fp_fn_analysis['false_negatives'])}\")\n", "for i, fn in enumerate(fp_fn_analysis['false_negatives']):\n", "    print(f\"  {i+1}. {fn['location']} | Severity: {fn['severity']}\")\n", "    print(f\"     {fn['observation'][:100]}...\")\n", "\n", "print(f\"\\n➕ FALSE POSITIVES (Model Additions): {len(fp_fn_analysis['false_positives'])}\")\n", "for i, fp in enumerate(fp_fn_analysis['false_positives']):\n", "    print(f\"  {i+1}. {fp['location']} | Severity: {fp['severity']}\")\n", "    print(f\"     {fp['observation'][:100]}...\")\n", "\n", "# Visualize false positives and negatives\n", "if fp_fn_analysis['fn_analysis'] or fp_fn_analysis['fp_analysis']:\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # False negatives impact analysis\n", "    if fp_fn_analysis['fn_analysis']:\n", "        fn_impacts = [fn['impact_of_missing'] for fn in fp_fn_analysis['fn_analysis']]\n", "        fn_impact_counts = {impact: fn_impacts.count(impact) for impact in ['High', 'Medium', 'Low']}\n", "        ax1.bar(fn_impact_counts.keys(), fn_impact_counts.values(), color=['#e74c3c', '#f39c12', '#2ecc71'])\n", "        ax1.set_title('Impact of Missing Issues (False Negatives)')\n", "        ax1.set_ylabel('Count')\n", "    \n", "    # False positives validity analysis\n", "    if fp_fn_analysis['fp_analysis']:\n", "        fp_classifications = [fp['classification'] for fp in fp_fn_analysis['fp_analysis']]\n", "        fp_class_counts = {}\n", "        for classification in fp_classifications:\n", "            fp_class_counts[classification] = fp_class_counts.get(classification, 0) + 1\n", "        ax2.bar(fp_class_counts.keys(), fp_class_counts.values(), color=['#3498db', '#9b59b6', '#e67e22'])\n", "        ax2.set_title('Classification of Model Additions (False Positives)')\n", "        ax2.set_ylabel('Count')\n", "        ax2.tick_params(axis='x', rotation=45)\n", "    \n", "    # Overall false positive/negative distribution\n", "    categories = ['True Positives\\n(Matches)', 'False Negatives\\n(Missed)', 'False Positives\\n(Added)']\n", "    values = [len(observation_matches), len(fp_fn_analysis['false_negatives']), len(fp_fn_analysis['false_positives'])]\n", "    colors = ['#2ecc71', '#e74c3c', '#f39c12']\n", "    bars = ax3.bar(categories, values, color=colors)\n", "    ax3.set_title('Overall Issue Detection Performance')\n", "    ax3.set_ylabel('Count')\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, values):\n", "        height = bar.get_height()\n", "        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,\n", "                 str(value), ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # Performance metrics pie chart\n", "    total_gt = len(gt_observations)\n", "    total_model = len(model_observations)\n", "    precision = len(observation_matches) / total_model if total_model > 0 else 0\n", "    recall = len(observation_matches) / total_gt if total_gt > 0 else 0\n", "    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "    \n", "    metrics = ['Precision', 'Recall', 'F1-Score']\n", "    metric_values = [precision * 100, recall * 100, f1_score * 100]\n", "    bars = ax4.bar(metrics, metric_values, color=['#3498db', '#e74c3c', '#2ecc71'])\n", "    ax4.set_title('Overall Detection Metrics')\n", "    ax4.set_ylabel('Score (%)')\n", "    ax4.set_ylim(0, 100)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, metric_values):\n", "        height = bar.get_height()\n", "        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(f\"\\n📊 DETECTION PERFORMANCE METRICS\")\n", "print(\"=\" * 40)\n", "total_gt = len(gt_observations)\n", "total_model = len(model_observations)\n", "matches_count = len(observation_matches)\n", "precision = matches_count / total_model if total_model > 0 else 0\n", "recall = matches_count / total_gt if total_gt > 0 else 0\n", "f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "\n", "print(f\"Precision: {precision:.3f} ({precision*100:.1f}%)\")\n", "print(f\"Recall: {recall:.3f} ({recall*100:.1f}%)\")\n", "print(f\"F1-Score: {f1_score:.3f} ({f1_score*100:.1f}%)\")\n", "print(f\"Total GT Issues: {total_gt}\")\n", "print(f\"Total Model Issues: {total_model}\")\n", "print(f\"Successfully Matched: {matches_count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Overall Evaluation Summary Using Gemini\n", "\n", "Generate a comprehensive evaluation summary using all the analysis results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_overall_evaluation_summary(coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches) -> str:\n", "    \"\"\"Generate comprehensive evaluation summary using Gemini\"\"\"\n", "    print(\"📝 Generating overall evaluation summary using Gemini AI...\")\n", "    \n", "    # Prepare summary data\n", "    total_gt = len(gt_observations)\n", "    total_model = len(model_observations)\n", "    matches_count = len(observation_matches)\n", "    \n", "    # Calculate metrics\n", "    coverage_pct = coverage_results['coverage_percentage']\n", "    avg_heuristic_precision = np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    avg_heuristic_recall = np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    severity_match_pct = (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0\n", "    \n", "    summary_prompt = f\"\"\"\n", "    Generate a comprehensive evaluation summary for a UX audit model performance.\n", "    \n", "    EVALUATION RESULTS:\n", "    \n", "    1. OBSERVATION COVERAGE:\n", "    - Total Ground Truth Issues: {total_gt}\n", "    - Total Model Issues: {total_model}\n", "    - Successfully Matched: {matches_count}\n", "    - Coverage Percentage: {coverage_pct:.1f}%\n", "    \n", "    2. HEURISTIC ACCURACY:\n", "    - Average Precision: {avg_heuristic_precision:.1f}%\n", "    - Average Recall: {avg_heuristic_recall:.1f}%\n", "    \n", "    3. SEVERITY CONSISTENCY:\n", "    - Exact Severity Matches: {severity_match_pct:.1f}%\n", "    \n", "    4. FALSE POSITIVES/NEGATIVES:\n", "    - False Negatives (Missed): {len(fp_fn_analysis['false_negatives'])}\n", "    - False Positives (Added): {len(fp_fn_analysis['false_positives'])}\n", "    \n", "    Provide a comprehensive evaluation summary including:\n", "    1. Overall model performance assessment\n", "    2. Key strengths identified\n", "    3. Major weaknesses and areas for improvement\n", "    4. Specific recommendations for model enhancement\n", "    5. Comparative analysis against expected performance\n", "    6. Final grade/rating (A-F scale)\n", "    \n", "    Format as a detailed professional evaluation report.\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(summary_prompt)\n", "        return response.text\n", "    except Exception as e:\n", "        return f\"Error generating summary: {e}\"\n", "\n", "# Generate overall evaluation summary\n", "evaluation_summary = generate_overall_evaluation_summary(\n", "    coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches\n", ")\n", "\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"📋 COMPREHENSIVE EVALUATION SUMMARY\")\n", "print(\"=\" * 80)\n", "print(evaluation_summary)\n", "print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Export Results\n", "\n", "Save all evaluation results to JSON files for further analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compile all results\n", "final_results = {\n", "    'evaluation_metadata': {\n", "        'timestamp': pd.Timestamp.now().isoformat(),\n", "        'total_gt_observations': len(gt_observations),\n", "        'total_model_observations': len(model_observations),\n", "        'evaluation_parameters': [\n", "            'Observation Coverage',\n", "            'Heuristic Match Accuracy', \n", "            'Severity Consistency',\n", "            'Location Accuracy',\n", "            'Observation Similarity',\n", "            'False Positives/Hallucinations',\n", "            'False Negatives/Omissions',\n", "            'Overall Evaluation Summary'\n", "        ]\n", "    },\n", "    'observation_coverage': coverage_results,\n", "    'observation_matches': observation_matches,\n", "    'heuristic_analysis': heuristic_analysis,\n", "    'severity_analysis': severity_analysis,\n", "    'false_positives_negatives': fp_fn_analysis,\n", "    'evaluation_summary': evaluation_summary,\n", "    'performance_metrics': {\n", "        'precision': matches_count / total_model if total_model > 0 else 0,\n", "        'recall': matches_count / total_gt if total_gt > 0 else 0,\n", "        'f1_score': f1_score,\n", "        'coverage_percentage': coverage_pct,\n", "        'severity_match_percentage': severity_match_pct,\n", "        'avg_heuristic_precision': avg_heuristic_precision,\n", "        'avg_heuristic_recall': avg_heuristic_recall\n", "    }\n", "}\n", "\n", "# Save results to JSON file\n", "output_filename = f'ux_audit_evaluation_results_{pd.Timestamp.now().strftime(\"%Y%m%d_%H%M%S\")}.json'\n", "with open(output_filename, 'w', encoding='utf-8') as f:\n", "    json.dump(final_results, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "print(f\"\\n💾 Evaluation results saved to: {output_filename}\")\n", "print(\"\\n✅ UX Audit Evaluation Complete!\")\n", "print(\"\\n📊 FINAL SUMMARY:\")\n", "print(f\"   • Coverage: {coverage_pct:.1f}%\")\n", "print(f\"   • Precision: {(matches_count / total_model * 100):.1f}%\")\n", "print(f\"   • Recall: {(matches_count / total_gt * 100):.1f}%\")\n", "print(f\"   • F1-Score: {(f1_score * 100):.1f}%\")\n", "print(f\"   • Heuristic Precision: {avg_heuristic_precision:.1f}%\")\n", "print(f\"   • Severity Consistency: {severity_match_pct:.1f}%\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}